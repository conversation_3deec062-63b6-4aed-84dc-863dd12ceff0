module.exports = {
  rules: {
    // Possible Errors
    'for-direction': 'error',
    'getter-return': ['warn'],
    'no-await-in-loop': 'warn',
    'no-compare-neg-zero': 'error',
    'no-cond-assign': ['warn', 'always'],
    'no-console': ['off'],
    'no-constant-condition': 'error',
    'no-control-regex': 'warn',
    'no-debugger': 'error',
    'no-dupe-args': 'error',
    'no-dupe-keys': 'error',
    'no-duplicate-case': 'error',
    'no-empty': ['error'],
    'no-empty-character-class': 'error',
    'no-ex-assign': 'error',
    'no-extra-boolean-cast': 'error',
    'no-extra-parens': ['off'],
    'no-extra-semi': 'error',
    'no-func-assign': 'error',
    'no-inner-declarations': ['error', 'both'],
    'no-invalid-regexp': ['error'],
    'no-irregular-whitespace': ['error'],
    'no-obj-calls': 'error',
    'no-prototype-builtins': 'error',
    'no-regex-spaces': 'warn',
    'no-sparse-arrays': 'error',
    'no-template-curly-in-string': 'error',
    'no-unexpected-multiline': 'error',
    'no-unreachable': 'error',
    'no-unsafe-finally': 'error',
    'no-unsafe-negation': 'error',
    'no-async-promise-executor': 'off',
    'use-isnan': 'error',
    'valid-typeof': ['error'],

    // Best Practices
    'accessor-pairs': ['warn'],
    'array-callback-return': ['warn', {allowImplicit: true}],
    'block-scoped-var': 'warn',
    'class-methods-use-this': ['warn'],
    complexity: ['warn', {max: 120}],
    'consistent-return': ['error'],
    curly: ['error'],
    'default-case': ['error'],
    'dot-location': ['error', 'property'],
    'dot-notation': ['warn'],
    eqeqeq: ['error', 'smart'],
    'guard-for-in': 'off',
    'no-alert': 'warn',
    'no-caller': 'error',
    'no-case-declarations': 'warn',
    'no-div-regex': 'error',
    'no-else-return': ['off'],
    'no-empty-function': ['warn'],
    'no-empty-pattern': 'error',
    'no-eq-null': 'error',
    'no-eval': ['error'],
    'no-extend-native': ['warn'],
    'no-extra-bind': 'error',
    'no-extra-label': 'error',
    'no-fallthrough': [
      'error',
      {commentPattern: 'break[\\s\\w]*omitted | no\\s?break'},
    ],
    'no-floating-decimal': 'error',
    'no-global-assign': ['error'],
    'no-implicit-coercion': ['off'],
    'no-implicit-globals': ['off'],
    'no-implied-eval': ['error'],
    'no-invalid-this': 'warn',
    'no-iterator': 'error',
    'no-labels': 'error',
    'no-lone-blocks': 'error',
    'no-loop-func': 'error',
    'no-magic-numbers': ['off'],
    'no-multi-spaces': [
      'warn',
      {
        exceptions: {
          Property: true,
          VariableDeclarator: true,
          ImportDeclaration: true,
        },
      },
    ],
    'no-multi-str': 'error',
    'no-new': 'error',
    'no-new-func': 'error',
    'no-new-wrappers': 'error',
    'no-octal': 'error',
    'no-octal-escape': 'error',
    'no-proto': 'error',
    'no-redeclare': ['error'],
    'no-restricted-properties': 'off',
    'no-return-assign': 'error',
    'no-return-await': 'warn',
    'no-script-url': 'error',
    'no-self-assign': ['error'],
    'no-self-compare': 'error',
    'no-sequences': ['warn'],
    'no-throw-literal': 'error',
    'no-unmodified-loop-condition': 'error',
    'no-unused-expressions': ['error'],
    'no-unused-labels': 'error',
    'no-useless-call': 'error',
    'no-useless-concat': 'error',
    'no-useless-escape': 'warn',
    'no-useless-return': 'error',
    'no-void': 'error',
    'no-warning-comments': 'warn',
    'no-with': 'error',
    radix: 'error',
    'require-await': 'warn',
    'vars-on-top': 'warn',
    'wrap-iife': ['warn', 'inside'],
    yoda: ['error', 'never', {exceptRange: true}],

    // Strict Mode
    strict: ['off'],

    // Variables
    'init-declarations': ['error'],
    'no-catch-shadow': 'error',
    'no-delete-var': 'error',
    'no-label-var': 'error',
    'no-restricted-globals': ['warn'],
    'no-shadow': ['warn'],
    'no-shadow-restricted-names': 'error',
    'no-undef': ['off'],
    'no-undef-init': 'error',
    'no-undefined': 'error',
    'no-unused-vars': ['warn'],
    'no-use-before-define': ['warn'],

    // Node.js and CommonJS
    'callback-return': ['error'],
    'handle-callback-err': ['warn'],
    'no-buffer-constructor': 'warn',
    'no-mixed-requires': ['warn'],
    'no-new-require': 'warn',
    'no-path-concat': 'error',
    'no-process-env': 'off',
    'no-process-exit': 'warn',
    'no-restricted-modules': ['warn'],
    'no-sync': ['warn'],

    // Stylistic Issues
    // 'array-bracket-newline': ['error', {multiline: true}],
    'array-bracket-spacing': ['error', 'never'],
    'block-spacing': ['error', 'always'],
    'brace-style': ['error', '1tbs', {allowSingleLine: false}],
    camelcase: ['warn', {properties: 'always'}],
    'capitalized-comments': ['error'],
    'comma-dangle': [
      'error',
      'only-multiline',
      // {
      //   functions : 'never',
      //   imports   : 'never',
      //   exports   : 'never'
      // }
    ],
    'comma-spacing': ['error', {before: false, after: true}],
    'comma-style': ['error', 'last'],
    'computed-property-spacing': ['error', 'never'],
    'consistent-this': ['warn'],
    'eol-last': ['error', 'always'],
    'func-call-spacing': ['error', 'never'],
    'func-name-matching': ['warn'],
    'func-names': ['off'],
    'func-style': ['error', 'expression'],
    // 'function-paren-newline':    ['error', 'multiline'],
    'id-blacklist': ['warn'],
    'id-length': ['off'],
    'id-match': ['off'],
    // 'implicit-arrow-linebreak': ['error', 'beside'],
    // indent:                      ['error', 2],
    'jsx-quotes': ['error'],
    'key-spacing': [
      'error',
      {
        beforeColon: false,
        afterColon: true,
        mode: 'minimum',
      },
    ],
    'keyword-spacing': ['error'],
    'line-comment-position': ['off'],
    'linebreak-style': ['error', 'unix'],
    'lines-around-comment': ['off'],
    'lines-between-class-members': ['error', 'always'],
    'max-depth': ['warn', {max: 5}],
    'max-len': ['off'],
    'max-lines': ['off'],
    'max-nested-callbacks': ['error'],
    'max-params': ['warn', {max: 5}],
    'max-statements': ['warn', {max: 120}, {ignoreTopLevelFunctions: true}],
    'max-statements-per-line': ['warn', {max: 2}],
    'multiline-comment-style': ['warn', 'starred-block'],
    'multiline-ternary': ['error', 'always-multiline'],
    'new-cap': ['warn'],
    'new-parens': ['error'],
    // 'newline-per-chained-call': ['error'],
    'no-array-constructor': ['error'],
    'no-bitwise': ['warn'],
    'no-continue': 'off',
    'no-inline-comments': 'off',
    'no-lonely-if': 'error',
    'no-mixed-operators': ['error'],
    'no-mixed-spaces-and-tabs': ['error'],
    'no-multi-assign': 'error',
    'no-multiple-empty-lines': ['error'],
    'no-negated-condition': 'error',
    'no-nested-ternary': 'warn',
    'no-new-object': 'error',
    'no-plusplus': ['off'],
    'no-restricted-syntax': ['off'],
    'no-tabs': 'error',
    'no-ternary': 'off',
    'no-trailing-spaces': ['error'],
    'no-underscore-dangle': ['warn'],
    'no-unneeded-ternary': ['error'],
    'no-whitespace-before-property': 'error',
    'nonblock-statement-body-position': ['off'],
    'object-curly-newline': ['error', {consistent: true}],
    'object-curly-spacing': ['error', 'never'],
    'object-property-newline': ['error', {allowAllPropertiesOnSameLine: true}],
    'one-var': ['off'],
    'one-var-declaration-per-line': ['off'],
    'operator-assignment': ['warn'],
    // 'operator-linebreak': ['error', 'before'],
    'padded-blocks': ['error', 'never'],
    'padding-line-between-statements': ['off'],
    quotes: ['error', 'single', {avoidEscape: true}],
    semi: ['warn', 'never'],
    'semi-spacing': ['error', {before: false, after: true}],
    'semi-style': 'error',
    'sort-keys': ['off'],
    'sort-vars': ['off'],
    'space-before-blocks': ['error', 'always'],
    'space-before-function-paren': [
      'error',
      {
        anonymous: 'never',
        named: 'never',
        asyncArrow: 'always',
      },
    ],
    'space-in-parens': ['error', 'never'],
    'space-infix-ops': ['error'],
    'space-unary-ops': [
      'error',
      {
        words: true,
        nonwords: false,
      },
    ],
    'spaced-comment': ['error', 'always'],
    'switch-colon-spacing': ['error'],
    'template-tag-spacing': ['error'],
    'unicode-bom': ['error', 'never'],
    'wrap-regex': 'off',

    // ECMAScript 6
    'arrow-body-style': ['off'],
    'arrow-parens': ['error', 'as-needed'],
    'arrow-spacing': ['error'],
    'constructor-super': 'error',
    'generator-star-spacing': [
      'error',
      {
        before: false,
        after: true,
      },
    ],
    'no-class-assign': 'error',
    'no-confusing-arrow': ['off'],
    'no-const-assign': 'error',
    'no-dupe-class-members': 'error',
    'no-duplicate-imports': ['error'],
    'no-new-symbol': 'error',
    'no-restricted-imports': ['warn'],
    'no-this-before-super': 'error',
    'no-useless-computed-key': 'error',
    'no-useless-constructor': 'error',
    'no-useless-rename': ['error'],
    'no-var': 'error',
    'object-shorthand': ['warn'],
    'prefer-arrow-callback': ['error'],
    'prefer-const': ['error'],
    'prefer-numeric-literals': ['off'],
    'prefer-rest-params': 'warn',
    'prefer-spread': 'error',
    'prefer-template': 'warn',
    'require-yield': 'error',
    'rest-spread-spacing': ['error', 'never'],
    'sort-imports': ['off'],
    'symbol-description': 'error',
    'template-curly-spacing': ['error', 'never'],
    'yield-star-spacing': [
      'error',
      {
        before: false,
        after: true,
      },
    ],
  },
}
