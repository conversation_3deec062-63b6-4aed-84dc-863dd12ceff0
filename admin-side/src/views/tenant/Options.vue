<script setup>
  import CElementCover from '@/components/Table/CElementCover.vue'
  import {useTenantOptions} from '@/composables/useTenantOptions'
  import {CButton} from '@coreui/vue'
  import {onMounted, ref, watch} from 'vue'
  import AuctionSetting from '../../components/tenant/options/AuctionSetting.vue'
  import FunctionSetting from '../../components/tenant/options/FunctionSetting.vue'

  const {functionOptions, bidOptions, getTenantOptions} = useTenantOptions()

  const loading = ref(false)
  const tabPanePillsActiveKey = ref(1)
  const triggerSaveBtn = ref(0)

  const refresh = async () => {
    loading.value = true
    await getTenantOptions()
    loading.value = false
  }

  const saveBtnClick = async () => {
    triggerSaveBtn.value++
  }

  onMounted(async () => {
    await refresh()
  })

  watch(
    () => tabPanePillsActiveKey.value,
    newVal => {
      refresh()
    }
  )
</script>
<template>
  <CCard class="text-center mb-3">
    <CCardHeader class="d-flex justify-content-between">
      <CNav variant="tabs" class="card-header-tabs">
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 1"
            @click="
              () => {
                tabPanePillsActiveKey = 1
              }
            "
          >
            機能
          </CNavLink>
        </CNavItem>
        <CNavItem>
          <CNavLink
            href="#"
            :active="tabPanePillsActiveKey === 2"
            @click="
              () => {
                tabPanePillsActiveKey = 2
              }
            "
          >
            入札仕様
          </CNavLink>
        </CNavItem>
      </CNav>
      <CButton color="primary" class="float-right" @click="saveBtnClick">
        保存
      </CButton>
    </CCardHeader>
    <CCardBody>
      <CTabContent>
        <CTabPane
          role="tabpanel"
          aria-labelledby="home-tab"
          :visible="tabPanePillsActiveKey === 1"
        >
          <FunctionSetting
            :options="functionOptions"
            :triggerSaveBtn="triggerSaveBtn"
            @refresh="refresh"
          />
        </CTabPane>
        <CTabPane
          role="tabpanel"
          aria-labelledby="profile-tab"
          :visible="tabPanePillsActiveKey === 2"
        >
          <AuctionSetting
            :options="bidOptions"
            :triggerSaveBtn="triggerSaveBtn"
            @refresh="refresh"
          />
        </CTabPane>
      </CTabContent>
    </CCardBody>
    <CElementCover
      :boundaries="[0, 0, 0, 0]"
      :center="true"
      :opacity="0.6"
      v-show="loading"
    />
  </CCard>
</template>
