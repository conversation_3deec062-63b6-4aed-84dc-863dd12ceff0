<script setup>
  import FileSelection from '@/components/common/fileSelection.vue'

  const model = defineModel()
  const emit = defineEmits(['onChange'])
  const props = defineProps({
    inputType: {
      type: String,
      required: true,
    },
    logicalName: {
      type: String,
      required: true,
    },
    physicalName: {
      type: String,
      required: true,
    },
    requiredFlag: {
      type: Number,
      default: 0,
    },
    options: {
      type: Array,
      default: () => [],
    },
    errorMessage: {
      type: String,
      default: '',
    },
  })

  const onFileChange = files => {
    model.value = files
    emit('onChange', files)
  }
</script>
<template>
  <div>
    <CRow class="mb-3">
      <CCol sm="3" class="fw-bolder d-flex align-items-start">
        <label>{{ logicalName }}</label>
        <CBadge v-if="requiredFlag === 1" color="danger" class="ms-auto"
          >必須</CBadge
        >
      </CCol>
      <CCol sm="4">
        <template v-if="inputType === 'text'">
          <CFormInput
            :name="physicalName"
            v-model="model"
            :invalid="!!errorMessage"
            :feedback-invalid="errorMessage"
            @change="
              val => {
                emit('onChange', val)
              }
            "
          />
        </template>
        <template v-else-if="inputType === 'textarea'">
          <CFormTextarea :name="physicalName" v-model="model" />
        </template>
        <template v-else-if="inputType === 'select'">
          <CFormSelect
            :name="logicalName"
            :options="options"
            v-model="model"
            @change="
              val => {
                emit('onChange', val)
              }
            "
          />
        </template>
        <template v-else-if="inputType === 'file'">
          <FileSelection
            :name="physicalName"
            :items="model"
            :maxFileCount="1"
            :maxFileSize="5 * 1024 * 1024"
            :errorMessage="errorMessage"
            @onChange="onFileChange"
          />
        </template>
      </CCol>
    </CRow>
  </div>
</template>
