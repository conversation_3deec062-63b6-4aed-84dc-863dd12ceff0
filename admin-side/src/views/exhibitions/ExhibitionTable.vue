<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header"> <CIcon name="info" />入札会情報 </slot>
    </CCardHeader>
    <CCardBody>
      <CRow class="mb-3">
        <CCol sm="3">
          <label>入札会名</label>
        </CCol>
        <CCol sm="9">
          <label>{{ exhibitionInfo.exhibitionName }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="3">
          <label>方式</label>
        </CCol>
        <CCol sm="9">
          <label>{{ exhibitionInfo.method }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="3">
          <label>ステータス</label>
        </CCol>
        <CCol sm="9">
          <label>{{ exhibitionInfo.status }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="3">
          <label>入札会日時</label>
        </CCol>
        <CCol sm="9">
          <label>{{ exhibitionInfo.bidDateTime }}</label>
        </CCol>
      </CRow>
    </CCardBody>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header">
        <CIcon name="cil-grid" />{{ caption }}
        <span name="total-count" style="float: right; margin-left: 30px"
          >総件数: {{ totalCount }}件</span
        >
        <span name="current-count" style="float: right"
          >検索結果: {{ currentCount }}件</span
        >
      </slot>
    </CCardHeader>
    <CCardBody>
      <div>
        <CRow>
          <CCol
            md="2"
            class="mb-3 mb-xl-0 text-right d-grid"
            style="padding-right: 0px"
          >
            <CButton
              :disabled="
                isReadOnly ||
                !(
                  exhibitionInfo &&
                  exhibitionInfo.exhibitionName &&
                  !exhibitionInfo.isEnded &&
                  exhibitionInfo.status === '落札結果未確定'
                )
              "
              size="sm"
              color="primary"
              @click="addNewItem"
              block
              >出展追加</CButton
            >
          </CCol>
          <CCol col="2" />
          <CCol sm="5" />
        </CRow>
      </div>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
      <CDataTable
        ref="lotTable"
        hover
        striped
        border
        sorter
        small
        fixed
        :id="'tableLayout'"
        style="font-size: 14px"
        :items="normalizedItems"
        :loading="loading"
        :fields="fields"
        :items-per-page="itemsPerPage"
        :active-page="activePage"
        :itemsPerPageSelect="itemsPerPageSelect"
        @pagination-change="paginationChange"
        @page-change="pageChange"
        :sorter-value="itemsSorter"
        @update:sorter-value="sorterChange"
      >
        <template #row_number="{index}">
          <td class="text-center" style="width: 50px">
            {{ index + 1 }}
          </td>
        </template>
        <template #productName="{item}">
          <td
            :name="`td_${item.exhibitionItemNo}`"
            class="text-left"
            style="min-width: 120px"
          >
            <span
              v-c-tooltip="{
                content: item.localized_json_array[0].f2.productName,
              }"
            >
              {{
                String(item.localized_json_array[0].f2.productName).length >
                productNameMaxLength
                  ? String(item.localized_json_array[0].f2.productName).slice(
                      0,
                      productNameMaxLength
                    ) + '…'
                  : item.localized_json_array[0].f2.productName
              }}
            </span>
          </td>
        </template>
        <template #maker="{item}">
          <td
            class="text-left"
            style="text-align: left; max-width: 150px; min-width: 90px"
          >
            {{ item.maker }}
          </td>
        </template>
        <template #rank="{item}">
          <td
            class="text-left"
            style="text-align: left; max-width: 150px; min-width: 100px"
          >
            {{ item.rank }}
          </td>
        </template>
        <template #product_detail="{item}">
          <td class="text-center px-3" style="width: 80px">
            <CButton
              color="info"
              :disabled="exhibitionEndFlag || isReadOnly"
              size="sm"
              block
              class="btn-edit"
              @click="editItem(item.item_no)"
              >編集</CButton
            >
          </td>
        </template>
        <template #lotItemSetting="{item}">
          <td class="text-center px-3" style="width: 110px">
            <CButton
              v-if="item.cancel_flag === 0 && exhibitionStartFlag"
              :disabled="
                exhibitionEndFlag || item.cancel_flag > 0 || isReadOnly
              "
              size="sm"
              color="danger"
              block
              :class="
                exhibitionEndFlag ||
                item.cancel_flag > 0 ||
                item.hummer_flag !== 0
                  ? 'btn button btn-dark disabled'
                  : ''
              "
              @click="lotCancel(item.lot_no)"
              >出品停止</CButton
            >
            <CButton
              v-if="item.cancel_flag === 0 && !exhibitionStartFlag"
              :disabled="isReadOnly"
              size="sm"
              color="danger"
              block
              :class="
                exhibitionEndFlag ||
                item.cancel_flag > 0 ||
                item.hummer_flag !== 0
                  ? 'btn button btn-dark disabled'
                  : ''
              "
              @click="lotDelete(item.lot_no)"
              >出品削除</CButton
            >
            <CButton
              v-if="
                item.cancel_flag === 1 &&
                !exhibitionEndFlag &&
                item.hummer_flag === 0
              "
              :disabled="isReadOnly"
              size="sm"
              color="warning"
              class="u-color-w pl-0 pr-0"
              block
              @click="lotRepublish(item.lot_no)"
              >出品停止取消</CButton
            >
            <span v-if="item.cancel_flag === 1 && exhibitionEndFlag"
              >出品停止中</span
            >
          </td>
        </template>
        <template #inquiries="{item}">
          <td class="text-center px-3" style="width: 95px">
            <CButton
              color="info"
              :disabled="isReadOnly"
              size="sm"
              block
              class="btn-edit"
              @click="inquiryItem(item.item_no)"
              >問合せ</CButton
            >
          </td>
        </template>
      </CDataTable>
      <CPagination
        :active-page="activePage"
        :pages="pages"
        @update:activePage="pageChange"
        align="center"
      />
    </CCardBody>
  </CCard>
</template>

<script>
  import noImage from '@/assets/noImage.jpg';
import Base from '@/common/base';
import CDataTable from '@/components/Table/CDataTable.vue';
import CPagination from '@/components/Table/CPagination.vue';
import {useAuthStore} from '@/store/auth';
import {useCommonStore} from '@/store/common';
import {itemsPerPageSelect} from '@/views/common/customTableView.js';
import {useRouter} from 'vue-router';

  export default {
    name: 'ExhibitionTable',
    components: {
      CDataTable,
      CPagination,
    },
    setup() {
      const store = useCommonStore();
      const router = useRouter();
      const {isReadOnly} = useAuthStore();
      return {
        store,
        router,
        itemsPerPageSelect,
        isReadOnly,
      };
    },
    props: {
      items: Array,
      exhibitionEndFlag: Boolean,
      currentCount: {
        type: String,
        default: '0',
      },
      totalCount: {
        type: String,
        default: '0',
      },
      exhibitionInfo: {
        type: Object,
        default() {
          return {};
        },
      },
      fields: {
        type: Array,
        default() {
          return [
            {
              key: 'manage_no',
              label: '商品ID',
              _classes: 'text-left break-white-space',
            },
            {
              key: 'productName',
              label: '商品名',
              _classes: 'text-left break-white-space',
              _style: 'text-align: center !important;',
            },
            // {key : 'quantity', label : '数量', _classes : 'data-align-right', _style : ' padding-right: 10px;'},
            {
              key: 'lowest_bid_info',
              label: '最低入札価格',
              _classes: 'text-left break-line',
              _style: 'text-align: center !important;',
            },
            {
              key: 'lowest_bid_accept_info',
              label: '最低落札価格',
              _classes: 'text-left break-line',
              _style: 'text-align: center !important;',
            },
            {
              key: 'product_detail',
              label: '詳細',
              _classes: 'text-center',
              sorter: false,
            },
            {
              key: 'lotItemSetting',
              label: '停止・削除',
              _classes: 'text-center',
              sorter: false,
            },
            {
              key: 'inquiries',
              label: '問合せ',
              _classes: 'text-center',
              sorter: false,
            },
          ];
        },
      },
      caption: {
        type: String,
        default: 'lotTable',
      },
      loading: Boolean,
      activePage: Number,
      itemsPerPage: Number,
      pages: Number,
      itemsSorter: Object,
      exhibitionStartFlag: Boolean,
      searched: {
        type: Array,
        require: false,
      },
    },
    data() {
      return {
        // Is delete button clicked
        unit: '円',
        isDeleteBtnClicked: false,
        btnClicked: false,
        productNameMaxLength: 40,
      };
    },
    created() {
      /*
       * デフォルトの並べ替えの設定: manage_no (昇順)
       * this.sorterChange({ asc: true, column: 'manage_no' });
       */
    },
    computed: {
      truncateText(text, limit) {
        if (text.length > limit) {
          return `${text.substring(0, limit)}...`;
        }
        return text;
      },

      // CDataTable にパラメータを渡す前にデータを正規化する
      normalizedItems() {
        if (!this.items || !this.items.length) {
          return [];
        }
        return this.items.map(item => {
          return {
            ...item,
            manage_no: item.manage_no,
            productName: item.localized_json_array[0].f2.productName,
            category: item.localized_json_array[0].f2.category,
            rank: item.localized_json_array[0].f2.rank,
            model: item.localized_json_array[0].f2.model,
            lowest_bid_info: `${this.priceLocaleString(item.lowest_bid_price)} ${this.unit}`,
            lowest_bid_accept_info: `${this.priceLocaleString(item.lowest_bid_accept_price)} ${this.unit}`,
          };
        });
      },
      noImage() {
        return noImage;
      },
    },

    watch: {
      items: {
        handler(newVal, oldVal) {
          console.log('items changed');
        },
        deep: true,
        immediate: false,
      },
    },
    methods: {
      pageChange(val) {
        if (this.items.length > 0) {
          this.$emit('page-change', val);
        }
      },
      sorterChange(val) {
        this.$emit('sorter-change', val);
      },
      paginationChange(val) {
        this.$emit('pagination-change', val);
      },
      lotDelete(val) {
        this.$emit('lot-delete', val);
      },
      lotCancel(val) {
        this.$emit('lot-cancel', val);
      },
      lotRepublish(val) {
        this.$emit('lot-republish', val);
      },
      getLotId(prefix, lotId) {
        console.log('getLotId:', prefix, lotId);
        if (prefix && lotId) {
          return prefix + lotId;
        }
        return null;
      },
      resetIsDeleteBtnClicked() {
        this.isDeleteBtnClicked = false;
      },
      priceLocaleString(value) {
        return Base.priceLocaleString(value);
      },
      biddingStatusItem(productNo, productName) {
        if (this.btnClicked) {
          return;
        }
        this.btnClicked = true;
        this.$router
          .push({
            name: '入札状況管理',
            params: {
              prop_item_no: productNo,
              prop_product_name: productName,
            },
          })
          .catch(err => {
            console.log('err: ', err);
          });
      },
      editItem(item_no) {
        console.log('editItem');
        if (this.btnClicked) {
          return;
        }
        this.btnClicked = true;
        this.store.set([
          'stockProps',
          {
            prop_item_no: item_no,
          },
        ]);
        this.$router.push(`/exhibitions/edit/${item_no}`);
      },
      inquiryItem(item_no) {
        if (this.btnClicked) {
          return;
        }
        this.btnClicked = true;
        this.$router.push(`/exhibitions/${item_no}/inquiryChat`);
      },
      addNewItem() {
        console.log('addNewItem: ', this.exhibitionInfo.exhibitionNo);
        if (this.btnClicked) {
          return;
        }
        this.btnClicked = true;
        this.$router
          .push({
            path: `/exhibitions/create/${this.exhibitionInfo.exhibitionNo}`,
          })
          .catch(err => {
            console.log('err: ', err);
          });
      },

      // 形式を yyyy-mm-dd hh:mm から yyyy/mm/dd hh:mm に変換します
      formatDateTime(datetime) {
        if (!datetime) {
          return '';
        }
        return datetime.replace(/-/g, '/');
      },
    },
  };
</script>

<style scoped lang="scss">
  td :deep(input) {
    padding: 3px;
  }

  .btn-lot {
    width: 100%;
  }

  .lot-id-prefix {
    text-align: left;
    display: inline-block;
  }

  .u-color-w,
  .u-color-w:hover,
  .u-color-w:focus {
    color: #fff;
  }
</style>

<style lang="scss">
  #tableLayout {
    .table-responsive {
      overflow-x: unset !important;
    }
  }

  .break-line {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
</style>
