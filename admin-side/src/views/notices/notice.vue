<template>
  <CCard class="mb-3">
    <CCardHeader class="form-inline">
      <label
        ><strong
          >お知らせ{{ this.$route.params.id ? '編集' : '登録' }}</strong
        ></label
      >
      <div style="margin-left: 10px">
        <scale-loader
          :loading="loading"
          :color="color"
          :height="height"
          :width="width"
        ></scale-loader>
      </div>
    </CCardHeader>
    <CCardBody>
      <CForm onsubmit="return false;">
        <CRow form class="mb-3" :key="'display_code'">
          <CCol sm="2"> 重要度 </CCol>
          <CCol sm="9">
            <CFormCheck
              v-for="option in options_notice_priority"
              :key="option.value"
              type="radio"
              :id="option.value"
              :value="option.value"
              :label="option.label"
              v-model="displayCode"
              inline
            />
          </CCol>
        </CRow>
        <CRow class="mb-3">
          <CCol sm="2" class="d-flex align-items-center">
            <label>お知らせ表示期間</label>
            <CBadge color="danger" class="ms-auto">必須</CBadge>
          </CCol>
          <CCol sm="auto">
            <CFormInput
              ref="stDate"
              type="date"
              v-model="startDate"
              :invalid="!dateValidate.startDate"
              :max="maxDate"
              @change="
                e => {
                  dateValidate.startDate = e.target.validity.valid;
                }
              "
            />
          </CCol>
          <CCol sm="auto" class="mx-0 d-flex align-items-center">
            <label>～</label>
          </CCol>
          <CCol sm="auto">
            <CFormInput
              :ref="'enDate'"
              type="date"
              v-model="endDate"
              :invalid="!dateValidate.endDate"
              :max="maxDate"
              @change="
                e => {
                  dateValidate.endDate = e.target.validity.valid;
                }
              "
            />
          </CCol>
        </CRow>
        <div v-for="lang in languages" :key="lang.value">
          <CRow class="mb-3" v-if="languages.length > 1">
            <h6>【{{ lang.label }}欄】</h6>
          </CRow>
          <CRow class="mb-3" v-show="false">
            <CCol sm="2">
              <label> ラベル</label>
            </CCol>
            <CCol sm="9">
              <CFormInput
                v-model="title1[lang.value]"
                @change="editorTextChange(lang.value)"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="d-flex align-items-center">
              <label>タイトル</label>
              <CBadge color="danger" class="ms-auto">必須</CBadge>
            </CCol>
            <CCol sm="9">
              <CFormInput
                v-model="title[lang.value]"
                @change="editorTextChange(lang.value)"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3 hide-form">
            <CCol sm="2">
              <label>サブタイトル</label>
            </CCol>
            <CCol sm="9">
              <CFormInput
                v-model="sub_title[lang.value]"
                @change="editorTextChange(lang.value)"
              />
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2" class="d-flex align-items-start">
              <label>本文</label>
              <label class="ms-auto">
                <CBadge color="danger">必須</CBadge>
              </label>
            </CCol>
            <CCol sm="9">
              <vue-editor
                v-model="body[lang.value]"
                @text-change="editorTextChange(lang.value)"
                :editor-toolbar="customToolbar"
              >
              </vue-editor>
            </CCol>
          </CRow>
          <CRow class="mb-3">
            <CCol sm="2">
              <label>添付ファイル</label>
            </CCol>
            <CCol sm="9">
              <FileSelection
                :ref="'fileUpload_' + lang.value"
                :key="lang.value"
                :items="file[lang.value]"
                :display_code="String(displayCode)"
                @onFilesChange="onFileCompChange($event, lang.value)"
              />
            </CCol>
          </CRow>
        </div>
      </CForm>
      <CElementCover v-if="loading" :opacity="0.8" />
    </CCardBody>
    <CCardFooter>
      <CButton class="mx-1" color="secondary" @click="goBack"
        >登録を中止して一覧に戻る</CButton
      >
      <CButton class="mx-1" color="primary" @click="openRegistModal"
        >{{ this.$route.params.id ? '更新' : '登録' }}する
      </CButton>
      <CButton
        color="danger"
        class="float-right"
        @click="openDeleteModal"
        v-if="this.$route.params.id"
        >削除</CButton
      >
      <CElementCover v-if="loading" :opacity="0.8" />
    </CCardFooter>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入力エラー</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>データの取得が失敗しました！</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="errorModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="registModal"
      @close="
        () => {
          registModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{ registModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loading && validateResult.length === 0">
          この内容で{{
            this.$route.params.id ? '更新' : '登録'
          }}してもよろしいですか？
        </div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="registModal = false"
          color="dark"
          :disabled="loading"
          v-if="validateResult.length === 0"
          >キャンセル
        </CButton>
        <CButton @click="btnRegistClicked" color="primary" :disabled="loading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="compModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{
          (this.$route.params.id ? '編集' : '登録') + '中止確認'
        }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            cancelModal = false;
            btn_clicked = false;
          "
          color="dark"
          >キャンセル</CButton
        >
        <CButton
          @click="
            () => {
              cancelModal = false;
              next();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="deleteModal"
      @close="
        () => {
          deleteModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loadingDelete">お知らせを削除してもよろしいですか？</div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loadingDelete"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="deleteModal = false"
          color="dark"
          :disabled="loadingDelete"
          >キャンセル</CButton
        >
        <CButton @click="deleteNotice" color="primary" :disabled="loadingDelete"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>
  </CCard>
</template>

<script>
  import Methods from '@/api/methods';
import {CElementCover, ScaleLoader} from '@/components/Table';
import {useAuthStore} from '@/store/auth';
import {
  CBadge,
  CButton,
  CCard,
  CCardBody,
  CCardFooter,
  CCardHeader,
  CCol,
  CForm,
  CFormCheck,
  CFormInput,
  CModal,
  CRow,
} from '@coreui/vue';
import {VueEditor} from 'vue3-editor';
import FileSelection from './fileSelection.vue';

  export default {
    name: 'notice',
    components: {
      FileSelection,
      ScaleLoader,
      VueEditor,
      CButton,
      CCard,
      CCardBody,
      CCardFooter,
      CCardHeader,
      CCol,
      CElementCover,
      CFormInput,
      CFormCheck,
      CModal,
      CRow,
      CBadge,
      CForm,
    },
    setup() {
      const {isReadOnly} = useAuthStore();
      return {isReadOnly};
    },
    data() {
      return {
        // Spinner
        color: '#5dc596',
        height: '10px',
        width: '4px',
        loading: false,

        // Text editor
        customToolbar: [
          [
            {
              header: [false, 1, 2, 3, 4, 5, 6],
            },
          ],
          ['bold', 'italic', 'underline', 'strike'],
          [{list: 'ordered'}, {list: 'bullet'}],
          [
            {
              color: [
                '#F00',
                '#e60000',
                '#ff9900',
                '#ffff00',
                '#008a00',
                '#0066cc',
                '#9933ff',
                '#ffffff',
                '#facccc',
                '#ffebcc',
                '#ffffcc',
                '#cce8cc',
                '#cce0f5',
                '#ebd6ff',
                '#bbbbbb',
                '#f06666',
                '#ffc266',
                '#ffff66',
                '#66b966',
                '#66a3e0',
                '#c285ff',
                '#888888',
                '#a10000',
                '#b26b00',
                '#b2b200',
                '#006100',
                '#0047b2',
                '#6b24b2',
                '#444444',
                '#5c0000',
                '#663d00',
                '#666600',
                '#003700',
                '#002966',
                '#3d1466',
              ],
            },
            {
              background: [
                '#F00',
                '#e60000',
                '#ff9900',
                '#ffff00',
                '#008a00',
                '#0066cc',
                '#9933ff',
                '#ffffff',
                '#facccc',
                '#ffebcc',
                '#ffffcc',
                '#cce8cc',
                '#cce0f5',
                '#ebd6ff',
                '#bbbbbb',
                '#f06666',
                '#ffc266',
                '#ffff66',
                '#66b966',
                '#66a3e0',
                '#c285ff',
                '#888888',
                '#a10000',
                '#b26b00',
                '#b2b200',
                '#006100',
                '#0047b2',
                '#6b24b2',
                '#444444',
                '#5c0000',
                '#663d00',
                '#666600',
                '#003700',
                '#002966',
                '#3d1466',
              ],
            },
          ],
          ['link', 'image', 'video'],
          ['clean'],
        ],

        // Screen options
        orig_notice: null,
        notice: null,
        languages: [],
        registModal: false,
        registConfirmDialog: false,
        compModal: false,
        errorModal: false,
        options_notice_priority: [],
        changeFlag: false,
        cancelModal: false,

        // File
        testS3UploadFile: null,
        btnRegist: false,

        // Screen params
        displayCode: null,
        startDate: '',
        endDate: '',
        title: {},
        title1: {},
        sub_title: {},
        body: [],
        link_url: [],
        file: [],

        // Validation
        validateResult: [],
        registModalTitle: '確認',

        // Delete modal
        deleteModal: false,
        loadingDelete: false,
        btn_clicked: false,

        // Date validation
        dateValidate: {
          startDate: true,
          endDate: true,
        },

        // 選択できる最大日付
        maxDate: '',

        constLanguageList: [
          {value: 'ja', label: '日本語'},
          {value: 'en', label: '英語'},
          {value: 'fr', label: 'フランス語'},
          {value: 'de', label: 'ドイツ語'},
          {value: 'es', label: 'スペイン語'},
        ]
      };
    },
    beforeRouteLeave(to, from, next) {
      if (this.changeFlag) {
        this.next = next;
        this.cancelModal = true;
      } else {
        // eslint-disable-next-line callback-return
        next();
      }
    },
    watch: {
      startDate(newVal, oldVal) {
        if (oldVal.length !== 0) {
          this.changeFlag = true;
        }
      },
      endDate(newVal, oldVal) {
        if (oldVal.length !== 0) {
          this.changeFlag = true;
        }
      },
      'body.lang.code'(newVal, oldVal) {
        console.log(newVal);
        this.changeFlag = true;
      },
    },
    mounted() {
      console.log('mounted');
      this.onMounted();
      if (this.isReadOnly) {
        // 一般の管理者の場合は[/dashboard]ページに遷移する
        this.$router.push({path: '/dashboard'});
      }
    },
    methods: {
      onMounted() {
        this.loading = true;
        this.getConstantsData()
          .then(() => {
            this.getNoticeData();
            this.getMaxDate();
          })
          .catch(error => {
            console.log('error: ', error);
            this.loading = false;
            Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getConstantsData() {
        const request = {
          key_strings: ['NOTICE_PRIORITY'],
        };
        // Request to server
        return Methods.apiExecute('get-constants-by-keys', request).then(
          response => {
            if (response.status === 200) {
              this.options_notice_priority = [];
              for (const constant of response.data) {
                switch (constant.key_string) {
                  case 'NOTICE_PRIORITY':
                    this.options_notice_priority.push({
                      value: constant.value1,
                      label: constant.value2,
                    });
                    break;
                  default:
                    break;
                }
              }
              this.languages = [];
              return Methods.apiExecute('get-tenant-language-list', {}).then(
                response => {
                  if (response.status === 200) {
                    // 言語
                    this.languages = response.data.language_code_list.map(
                      cd => {
                        const lang_list = this.constLanguageList.find(
                          item => item.value === cd
                        );
                        return {
                          value: cd,
                          label: lang_list ? lang_list.label : '',
                        };
                      }
                    );
                  }
                  return Promise.resolve();
                }
              );
            }
            return Promise.resolve();
          }
        );
      },
      getNoticeData() {
        const id = this.$route.params.id;

        // Original初期化
        this.orig_notice = null;

        if (typeof id === 'undefined' || id === null) {
          this.loading = false;
          // Add new mode
          this.showScreen();
          return Promise.resolve();
        }
        // Edit mode
        const search_condition = {
          notice_no: id,
        };

        return this.getNoticesFromServer(search_condition)
          .then(notices => {
            this.loading = false;
            if (typeof notices === 'undefined' || notices === null) {
              this.notice = null;
              this.errorModal = true;
            } else {
              this.notice = notices[0];
              console.log(`notice = ${JSON.stringify(this.notice)}`);
              // Copy not by reference
              this.orig_notice = JSON.parse(JSON.stringify(this.notice));
              this.showScreen();
            }
            return Promise.resolve();
          })
          .catch(error => {
            console.log(error);
            Methods.parseHtmlResponseError(this.$router, error);
          });
      },
      getMaxDate() {
        const today = new Date(Date.now() + (new Date().getTimezoneOffset() + 9 * 60) * 60 * 1000);
        const nextYearDate = new Date(today);
        nextYearDate.setDate(today.getDate() + 364);
        nextYearDate.setHours(0, 0, 0, 0); // 時間をリセットして日付のみ取得

        // YYYY-MM-DD形式で取得
        const year = nextYearDate.getFullYear();
        const month = String(nextYearDate.getMonth() + 1).padStart(2, '0');
        const day = String(nextYearDate.getDate()).padStart(2, '0');

        this.maxDate = `${year}-${month}-${day}`;
      },
      getNoticesFromServer(search_condition) {
        // Request to server
        return Methods.apiExecute(
          'get-notice-by-notice-no',
          search_condition
        ).then(response => {
          if (response.status === 200) {
            this.compModal = false;
            const noticeList = response.data;

            if (typeof noticeList === 'undefined' || noticeList === null) {
              return Promise.resolve(null);
            }
            // Processing data
            const tmpMap = new Map();
            for (const noti of noticeList.data) {
              const tmp1 = tmpMap.get(String(noti.notice_no));
              if (tmp1) {
                if (tmp1.language_code !== noti.language_code) {
                  tmp1.language_code.push(noti.language_code);
                  tmp1.title.push(noti.title);
                  tmp1.title1.push(noti.title1);
                  tmp1.sub_title.push(noti.sub_title);
                  tmp1.body.push(noti.body);
                  tmp1.link_url.push(noti.link_url);
                  tmp1.file.push(noti.file);
                }
              } else {
                const notiTmp = JSON.parse(JSON.stringify(noti));
                notiTmp.language_code = [noti.language_code];
                notiTmp.title = [noti.title];
                notiTmp.title1 = [noti.title1];
                notiTmp.sub_title = [noti.sub_title];
                notiTmp.body = [noti.body];
                notiTmp.link_url = [noti.link_url];
                notiTmp.file = [noti.file];
                tmpMap.set(String(notiTmp.notice_no), notiTmp);
              }
            }

            const finalList = Array.from(tmpMap.values());
            return Promise.resolve(finalList);
          }
          return Promise.resolve([]);
        });
      },
      showScreen() {
        if (this.notice) {
          this.displayCode = String(this.notice.display_code);
          // Start datetime
          this.startDate = Methods.getFormatDate(this.notice.start_datetime);
          // End datetime
          this.endDate = Methods.getFormatDate(this.notice.end_datetime);

          // Prepare data for each language
          this.title = {};
          this.title1 = {};
          this.sub_title = {};
          this.body = {};
          this.link_url = {};
          this.file = {};
          for (const i in this.languages) {
            const lang = this.languages[i];
            const j = this.notice.language_code.indexOf(lang.value);
            if (j > -1) {
              this.title[lang.value] = this.notice.title[j];
              this.title1[lang.value] = this.notice.title1[j];
              this.sub_title[lang.value] = this.notice.sub_title[j];
              this.body[lang.value] = this.notice.body[j];
              this.link_url[lang.value] = this.notice.link_url[j];
              this.file[lang.value] = this.notice.file[j];
            } else {
              this.title[lang.value] = '';
              this.title1[lang.value] = '';
              this.sub_title[lang.value] = '';
              this.body[lang.value] = '';
              this.link_url[lang.value] = '';
              this.file[lang.value] = [];
            }
          }
        } else {
          // Default: 普通
          this.displayCode = '1';

          const options = {year: 'numeric', month: '2-digit', day: '2-digit'};
          const tmpDate = new Date();
          // Start datetime
          this.startDate = Methods.getFormatDate(
            tmpDate.toLocaleDateString('ja-JP', options)
          );
          // End datetime
          this.endDate = Methods.getFormatDate(
            tmpDate.toLocaleDateString('ja-JP', options)
          );

          this.title = {};
          this.title1 = {};
          this.sub_title = {};
          this.body = {};
          this.link_url = {};
          this.file = {};
          for (const i in this.languages) {
            const lang = this.languages[i];
            console.log(`lang = ${lang}`);
            this.title[lang.value] = '';
            this.title1[lang.value] = '';
            this.sub_title[lang.value] = '';
            this.body[lang.value] = '';
            this.link_url[lang.value] = '';
            this.file[lang.value] = [];
          }
        }
      },
      goBack() {
        if (this.btn_clicked) {
          return;
        }
        this.btn_clicked = true;
        this.$router.push({path: '/notices'});
      },
      openRegistModal() {
        this.registModalTitle = '確認';
        this.compModal = false;
        this.registModal = true;
        this.loading = false;
        this.validateResult = [];
      },
      registNotice() {
        // Get data from screen
        this.getScreenInputData();

        if (!this.notice) {
          console.log('notice is empty!');
        }

        console.log(`notice = ${JSON.stringify(this.notice)}`);

        /*
         * Prepare move or delete files
         * This.getMoveOrDeleteFiles((move_files, delete_files) => {
         */
        const send_data = {
          data: this.notice,
          move_files: null,
          delete_files: null,
        };
        console.log(`send_data = ${JSON.stringify(send_data)}`);

        const api_name = this.$route.params.id
          ? 'edit-notice'
          : 'create-notice';
        return Methods.apiExecute(api_name, send_data)
          .then(response => {
            this.registModal = false;
            console.log(`response = ${JSON.stringify(response)}`);
            if (response.status === 200) {
              this.loading = false;
              // This.compModal = true
              this.changeFlag = false;
              this.$router.push({path: '/notices'});
            }
            return Promise.resolve();
          })
          .catch(error => {
            console.log(error);
            this.loading = false;
            this.registModal = false;
            Methods.parseHtmlResponseError(this.$router, error);
            return Promise.resolve();
          });
        // });
      },
      getScreenInputData() {
        if (typeof this.notice === 'undefined' || this.notice === null) {
          this.notice = {};
        }
        // Display code
        this.notice.display_code = Number(this.displayCode);

        const options = {year: 'numeric', month: '2-digit', day: '2-digit'};
        // Start date
        let tmpDate = '';
        if (this.dateValidate.startDate) {
          if (this.startDate === '') {
            this.notice.start_datetime = this.startDate;
          } else {
            tmpDate = new Date(this.startDate);
            this.notice.start_datetime = `${tmpDate.toLocaleDateString('ja-JP', options)} 00:00:00`;
          }
        } else {
          this.notice.start_datetime = 'Invalid Date';
        }
        // End date
        tmpDate = '';
        if (this.dateValidate.endDate) {
          if (this.endDate === '') {
            this.notice.end_datetime = this.endDate;
          } else {
            tmpDate = new Date(this.endDate);
            this.notice.end_datetime = `${tmpDate.toLocaleDateString('ja-JP', options)} 23:59:59`;
          }
        } else {
          this.notice.end_datetime = 'Invalid Date';
        }

        this.notice.language_code = [];
        this.notice.language_name = [];
        this.notice.title = [];
        this.notice.title1 = [];
        this.notice.sub_title = [];
        this.notice.body = [];
        this.notice.link_url = [];
        this.notice.file = [];
        for (const i in this.languages) {
          const lang = this.languages[i];
          this.notice.language_code.push(lang.value);
          this.notice.language_name.push(lang.label);
          this.notice.title.push(this.title[lang.value]);
          this.notice.title1.push(this.title1[lang.value]);
          this.notice.sub_title.push(this.sub_title[lang.value]);
          this.notice.body.push(this.body[lang.value]);
          this.notice.link_url.push(this.link_url[lang.value]);

          let tmpFiles = '';
          if (
            typeof this.file[lang.value] !== 'undefined' &&
            this.file[lang.value] !== null
          ) {
            for (const index in this.file[lang.value]) {
              if (index > 0) {
                tmpFiles += ',';
              }
              tmpFiles += this.file[lang.value][index];
            }
          }
          tmpFiles = `{${tmpFiles}}`;
          this.notice.file.push(tmpFiles);
        }
      },
      onFileCompChange(files, lang_code) {
        console.log(`lang_code = ${JSON.stringify(lang_code)}`);
        console.log(`onFileCompChange = ${JSON.stringify(files)}`);
        this.changeFlag = true;

        const tmpFiles = [];
        for (const index in files) {
          if (
            typeof files[index].s3url !== 'undefined' &&
            files[index].s3url !== null &&
            files[index].s3url !== ''
          ) {
            tmpFiles.push(files[index].s3url);
          }
        }
        this.file[lang_code] = tmpFiles;
      },
      async btnRegistClicked() {
        console.log('btnRegistClicked');
        this.changeFlag = false;

        if (this.validateResult.length > 0) {
          // Close modal
          this.loading = false;
          this.registModal = false;
        } else {
          // Open modal
          this.loading = true;

          // Validation
          await this.validation();

          // エラーがない場合はデータ更新へ進める
          if (this.validateResult.length === 0) {
            // Upload file to s3
            await this.uploadRecursion(this.languages).then(resList => {
              // Update the newest file infos
              for (const res in resList) {
                this.onFileCompChange(
                  resList[res].files,
                  resList[res].lang_code
                );
              }
              // Regist notice to DB
              return this.registNotice();
            });
          } else {
            this.loading = false;
            this.registModalTitle = '入力エラー';
          }
        }
      },
      uploadRecursion(languages) {
        return Promise.all(
          languages.map(lang => {
            return new Promise((resolve, reject) => {
              this.$refs[`fileUpload_${lang.value}`][0].uploadFiles(
                event,
                updatedFiles => {
                  return resolve({files: updatedFiles, lang_code: lang.value});
                }
              );
            });
          })
        ).then(resList => {
          console.log('resList', resList);
          return Promise.resolve(resList);
        });
      },
      validation() {
        this.validateResult = [];
        this.getScreenInputData();

        const validate_data = {
          validation_mode: true,
          data: this.notice,
        };
        console.log(`validate_data = ${JSON.stringify(validate_data)}`);

        const api_name = this.$route.params.id
          ? 'edit-notice'
          : 'create-notice';
        return Methods.apiExecute(api_name, validate_data)
          .then(response => {
            console.log(`validation response = ${JSON.stringify(response)}`);
            // Validation error results
            if (response.status === 400) {
              this.validateResult.push(response.message);
            }
            return Promise.resolve();
          })
          .catch(error => {
            console.log(JSON.stringify(error));
            this.validateResult = Methods.parseHtmlResponseError(
              this.$router,
              error
            )[0];
            return Promise.resolve();
          });
      },
      editorTextChange(code) {
        const i = this.notice?.language_code
          ? this.notice.language_code.indexOf(code)
          : -1;
        if (
          !this.$route.params.id ||
          i === -1 ||
          this.body[code] !== this.notice.body[i] ||
          this.title[code] !== this.notice.title[i] ||
          this.title1[code] !== this.notice.title1[i] ||
          this.sub_title[code] !== this.notice.sub_title[i]
        ) {
          this.changeFlag = true;
        }
      },
      openDeleteModal() {
        this.loadingDelete = false;
        this.deleteModal = true;
      },
      deleteNotice() {
        this.loadingDelete = true;
        const notice_no = this.$route.params.id;
        console.log(`deleteNotice: ${JSON.stringify(notice_no)}`);

        // Request to server
        Methods.apiExecute('delete-notice', {notice_no})
          .then(response => {
            console.log(`response = ${JSON.stringify(response)}`);
            this.loadingDelete = false;
            this.deleteModal = false;
            if (response.status === 200) {
              this.goBack();
            }
          })
          .catch(error => {
            console.log(error);
            this.loadingDelete = false;
            this.deleteModal = false;
            Methods.parseHtmlResponseError(this.$router, error);
          });
      },
    },
  };
</script>
<style lang="css">
  .ql-editor {
    min-height: 250px !important;
  }

  .hide-form {
    display: none !important;
  }

  .ql-clean {
    position: relative;
    display: inline-block;
  }

  .ql-clean:hover::after {
    content: '選択範囲のフォーマットをリセット';
    position: absolute;
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 5px;
    border-radius: 4px;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    z-index: 1000;
    font-size: 12px;
  }
</style>
