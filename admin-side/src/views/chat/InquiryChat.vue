<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>
          管理番号：<span class="pr-4">{{ manage_no }}</span> 出品区分：<span
            class="pr-4"
            >{{ classification_name }}</span
          >
          状態：出品中
        </strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <div style="margin: 0 30px">
            <div class="data-group" id="chat-area">
              <CRow v-for="(item, i) in chatData" :key="i">
                <template v-if="item.update_category_id === '2'">
                  <CCol sm="5">
                    <div class="chatText receiveChat">
                      <div class="mb-2">
                        {{ item.create_datetime }}　【質問者：{{
                          item.member_name
                        }}】
                      </div>
                      <div style="white-space: pre-line">
                        {{ item.message }}
                      </div>
                      <div class="form-inline">
                        <CButton
                          class="checkedBtn"
                          size="sm"
                          color="primary btn-status"
                          block
                          @click="answer(item.exhibition_message_no)"
                        >
                          回答する
                        </CButton>
                        <div
                          v-if="item.checked_admin_no !== null"
                          class="mt-4 ml-2"
                        >
                          回答済み
                        </div>
                      </div>
                    </div>
                  </CCol>
                </template>
                <template v-else>
                  <CCol sm="7"></CCol>
                  <CCol sm="5">
                    <div class="chatText sendChat">
                      <div class="mb-2">{{ item.create_datetime }}</div>
                      <div style="white-space: pre-line">
                        {{ item.message }}
                      </div>
                      <div class="mt-2">担当：{{ item.create_admin_name }}</div>
                    </div>
                  </CCol>
                </template>
              </CRow>
            </div>
            <div class="bottom-menu sticky">
              <CRow class="form-group">
                <CCol>
                  <div v-for="(text, i) in errorMsg" :key="i" class="text-red">
                    {{ text }}
                  </div>
                  <CTextarea
                    ref="chatInput"
                    class="form-group col-sm-12 mb-0 px-0-imp"
                    addInputClasses="fixed-size col-sm-12"
                    rows="6"
                    v-model="chatMessage"
                    @change="changeFlag = true"
                    :disabled="answerFlg"
                    placeholder="コメントを入力してください"
                  />
                </CCol>
              </CRow>
              <CRow>
                <CCol>
                  <CButton
                    class="mx-1 font-weight-bold"
                    color="light"
                    block
                    @click="goBack"
                  >
                    一覧に戻る
                  </CButton>
                </CCol>
                <CCol sm="7" />
                <CCol class="form-inline" style="display: block">
                  <span style="float: right" @click="getChatData">
                    <CIcon :height="30" name="cil-reload" class="reload-icon" />
                  </span>
                </CCol>
                <CCol>
                  <CButton
                    size="lg"
                    color="dark btn-status"
                    block
                    @click="reset"
                    :disabled="answerFlg"
                  >
                    クリア
                  </CButton>
                </CCol>
                <CCol>
                  <CButton
                    size="lg"
                    color="primary btn-status"
                    block
                    @click="regist"
                    :disabled="answerFlg"
                  >
                    投稿する
                  </CButton>
                </CCol>
              </CRow>
            </div>
          </div>
        </CForm>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
  import Methods from '@/api/methods';

  export default {
    name: 'chat',
    data() {
      return {
        exhibition_item_no: null,
        manage_no: null,
        classification_name: null,
        itemData: {},
        chatData: {},
        chatMessage: null,
        answerFlg: true,
        selectExhibitionMessageNo: null,
        loading: false,
        errorMsg: [],
      };
    },
    mounted() {
      if (this.$route.params.id) {
        console.log(this.$route.params.id)
        // this.getChatData()
        //   .then(() => {
        //     this.$nextTick(() => {
        //       document.getElementById(this.$refs.chatInput.safeId).focus();
        //     });
        //   })
        //   .catch(error => {
        //     this.loading = false;
        //     this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
        //   });
      } else {
        this.goBack();
      }
    },
    methods: {
      getChatData() {
        this.loading = true;
        this.exhibition_item_no = this.$route.params.id;
        this.manage_no = this.$route.params.manage_no;
        this.classification_name = this.$route.params.classification_name;
        const search_condition = {
          exhibition_item_no: this.exhibition_item_no,
        };
        return Methods.apiExecute('get-inquiry-chat', search_condition).then(
          response => {
            this.loading = false;
            this.chatData =
              response.data && response.data.length > 0 ? response.data : {};

            this.$nextTick(() => {
              const childNodes =
                document.getElementById('chat-area').childNodes;
              if (childNodes && childNodes.length > 0) {
                childNodes[childNodes.length - 1]?.scrollIntoView();
              }
            });
            return Promise.resolve();
          }
        );
      },
      answer(exhibition_message_no) {
        this.answerFlg = false;
        this.selectExhibitionMessageNo = exhibition_message_no;
        this.$nextTick(() => {
          document.getElementById(this.$refs.chatInput.safeId).focus();
        });
      },
      regist() {
        if (this.chatMessage) {
          this.loading = true;
          const params = {
            exhibition_item_no: this.exhibition_item_no,
            exhibition_message_no: this.selectExhibitionMessageNo,
            chat_message: this.chatMessage,
          };
          Methods.apiExecute('regist-inquiry-chat', params)
            .then(response => {
              if (response.status === 200) {
                this.chatMessage = null;
                this.errorMsg = [];
                this.loading = false;
                this.answerFlg = true;
                this.getChatData();
              }
            })
            .catch(error => {
              this.loading = false;
              this.errorMsg = Methods.parseHtmlResponseError(
                this.$router,
                error
              );
            });
        }
      },
      reset() {
        this.chatMessage = null;
      },
      goBack() {
        this.$router.go(-1);
      },
    },
  };
</script>

<style type="text/css">
  .chatText {
    border: 1px solid;
    padding: 10px;
    margin: 5px 0;
  }

  .receiveChat {
    background: #e2f0d9;
  }

  .sendChat {
    background: #deebf7;
  }

  .btn-confirm {
    margin: 5px 10px;
  }

  .bottom-menu {
    background: #fff;
    padding-top: 5px;
    padding-bottom: 5px;
    margin-right: 0;
    margin-left: 0;
  }

  .sticky {
    position: sticky;
    bottom: 0;
  }

  .fixed-size {
    resize: none;
  }

  .checkedBtn {
    margin-top: 20px;
    width: 15%;
  }

  .data-group {
    border: solid 1px black;
    padding: 5px 20px;
    margin-bottom: 10px;
    overflow-y: auto;
    /* overflow-x: hidden; */
    height: 500px;
  }

  .text-red {
    color: red;
  }

  .reload-icon {
    color: #ffff;
    background-color: #02b092d4;
    border-radius: 50%;
    padding: 5px;
    height: 100%;
    cursor: pointer;
  }
</style>
