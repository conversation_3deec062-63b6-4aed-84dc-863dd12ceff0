<template>
  <div name="file-list">
    <div v-if="loading" class="text-center">
      <CSpinner />
    </div>
    <div v-if="errorMessage" class="text-danger">
      {{ errorMessage }}
    </div>
    <div v-if="fileInfos">
      <div v-for="(fileInfo, index) in fileInfos" :key="index">
        <div class="row mb-2">
          <div class="col-sm-9">
            <a
              @contextmenu.prevent="onContextMenuOpen($event, fileInfo.s3url)"
              @click="fileNameClickEvent(fileInfo.s3url)"
              href="javascript:void(0);"
              :style="
                fileInfo.s3url
                  ? ''
                  : 'color: inherit; text-decoration: inherit;'
              "
            >
              {{ fileInfo.fileName }}
            </a>
          </div>
          <div class="col-sm-3 d-grid">
            <CButton
              name="btn-delete"
              :disabled="isBtnDisabled"
              :class="isBtnDisabled ? 'pt-not-allow' : ''"
              @click="openFileDeleteModal(fileInfo)"
              color="danger"
              style="width: 100%"
              >削除</CButton
            >
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-2">
      <div class="col-sm-6">
        <label
          name="btn-select"
          class="btn btn-block"
          :class="isBtnDisabled ? 'btn-disabled' : 'btn-secondary'"
        >
          参照...
          <input
            :ref="fileupload"
            :disabled="isBtnDisabled"
            type="file"
            multiple
            :data-max="maxFileCount"
            @change="selectFile"
            hidden
          />
        </label>
      </div>
      <div class="col-sm-4"></div>
    </div>

    <div class="row mb-2">
      <div class="col-sm-11">
        <div v-if="message" class="alert alert-light" role="alert">
          <ul>
            <li v-for="(ms, i) in message.split('\n')" :key="i">
              {{ ms }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="fileDeleteModal"
      @close="
        () => {
          fileDeleteModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>添付ファイルを削除してもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="fileDeleteModal = false" color="light"
          >キャンセル</CButton
        >
        <CButton @click="deleteFile" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="fileUploadModal"
      @close="
        () => {
          fileUploadModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle>アップロード確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>添付ファイルをアップロードしてもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="fileUploadModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="errorModal"
      @close="
        () => {
          errorModal = false
        }
      "
    >
      <CModalHeader>
        <CModalTitle>入力エラー</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>{{ errorMsg }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="errorModal = false" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>

    <ContextMenu v-model:show="contextMenu.show" :options="contextMenu.options">
      <context-menu-item label="ファイルを開く" @click="getFileViewUrl" />
      <context-menu-item label="ダウンロード" @click="getFileDownloadUrl" />
    </ContextMenu>
  </div>
</template>

<script setup>
  import UploadFile from '@/api/uploadFileToS3'
  import Base from '@/common/base'
  import {CButton, CModal, CSpinner} from '@coreui/vue'
  import {ContextMenu, ContextMenuItem} from '@imengyu/vue3-context-menu'
  import {ref, watch} from 'vue'
  import useFileDownload from '../../common/useFileDownload'

  const emit = defineEmits(['onChange', 'onUploaded'])
  const props = defineProps({
    items: Array,
    maxFileCount: {
      type: Number,
      default: 5,
    },
    maxFileSize: {
      type: Number,
      default: 5 * 1024 * 1024, // 5MB
    },
    isBtnDisabled: {
      type: Boolean,
      default: false,
    },
    btnRegist: {
      type: Boolean,
      default: false,
    },
    errorMessage: {
      type: String,
      default: '',
    },
  })

  const {download} = useFileDownload()

  // Select file
  const fileupload = ref(null)
  const selectedFiles = ref(null)
  const message = ref('')
  // Delete file
  const fileDeleteModal = ref(false)
  const deleteFileTemp = ref(null)
  // Upload file
  const fileUploadModal = ref(false)

  // Data
  const fileInfos = ref([])
  const deleteFiles = ref([])

  // エラーmodal
  const errorModal = ref(false)
  const errorMsg = ref('')

  const loading = ref(false)

  // Context menu
  const contextMenu = ref({
    show: false,
    options: {
      zIndex: 9999,
      minWidth: 230,
      x: 500,
      y: 500,
    },
    data: null,
  })

  const onChange = () => {
    console.log(`fileInfos: ${JSON.stringify(fileInfos.value)}`)
    emit('onChange', fileInfos.value)
  }
  const onUploaded = () => {
    console.log('onUploaded: emitted')
    emit('onUploaded', fileInfos.value)
  }

  const checkFileSize = file => {
    if (file?.size) {
      if (file.size > props.maxFileSize) {
        return true
      }
    }
    return null
  }

  const selectFile = () => {
    if (typeof fileInfos.value === 'undefined' || fileInfos.value === null) {
      fileInfos.value = []
    }

    // 5個まで登録できる
    if (fileInfos.value.length >= props.maxFileCount) {
      errorModal.value = true
      errorMsg.value = `添付ファイルは${props.maxFileCount}個までしか登録できません。`
      if (fileupload.value?.value) {
        fileupload.value.value = null
      }
      return
    }

    // Check if file size exceeds limit
    selectedFiles.value = []
    const errorFiles = []
    for (let i = 0; i < event.target.files.length; i++) {
      const f = event.target.files[i]
      if (checkFileSize(f)) {
        errorFiles.push(
          `ファイル「${f.name}」は${props.maxFileSize}MBを超えるため、登録できません。`
        )
        if (fileupload.value?.value) {
          fileupload.value.value = null
        }
        return
      } else {
        selectedFiles.value.push(f)
      }
    }
    if (fileupload.value?.value) {
      fileupload.value.value = null
    }
    if (errorFiles.length > 0) {
      errorModal.value = true
      errorMsg.value = errorFiles.join('\n')
      return
    }

    // Upload valid files
    loading.value = true
    upload(selectedFiles.value).then(results => {
      console.log(`upload results: ${JSON.stringify(results)}`)
      results.forEach((x, i) => {
        if (x.status === 200) {
          const currentLength = fileInfos.value.length || 0
          fileInfos.value.push({
            id: `local${currentLength + i}`,
            fileName: Base.getFileName(x.fileName),
            file: null,
            s3url: x.message,
          })
        } else {
          message.value += `ファイル「${x.fileName}」のアップロードに失敗しました。\n`
        }
      })
      loading.value = false
      onChange()
      onUploaded()
    })
  }
  const upload = fileList => {
    console.log('upload')
    if (typeof fileList === 'undefined' || fileList === null) {
      return Promise.resolve([])
    }
    return Promise.all(
      fileList.map(fileInfo => {
        return UploadFile.uploadPromise('item-ancillary', fileInfo)
          .then(data => {
            return Promise.resolve(data)
          })
          .catch(error => {
            return Promise.resolve(error)
          })
      })
    )
  }
  const openFileDeleteModal = file => {
    console.log('openFileDeleteModal')
    fileDeleteModal.value = true
    deleteFileTemp.value = file
  }
  const objectsAreIdentical = (obj1, obj2) => {
    return (
      obj1.length === obj2.length &&
      JSON.stringify(obj1) === JSON.stringify(obj2)
    )
  }
  const getFileDownloadUrl = () => {
    console.log('getFileDownloadUrl')
    // Get download url from file server
    const fileUrl = contextMenu.value.data
    if (fileUrl !== null && fileUrl !== '') {
      UploadFile.getDownloadUrl(fileUrl).then(res => {
        console.log(`res: ${JSON.stringify(res)}`)
        download(res, Base.getFileName(fileUrl))
      })
    }
  }
  const getFileViewUrl = () => {
    console.log('getFileViewUrl')
    // Get file viewing url from file server
    const fileUrl = contextMenu.value.data
    if (fileUrl !== null && fileUrl !== '') {
      UploadFile.getFileViewUrl(fileUrl).then(res => {
        console.log(`res: ${JSON.stringify(res)}`)
        window.open(res, '_blank')
      })
    }
  }
  const fileNameClickEvent = fileUrl => {
    console.log('fileNameClickEvent')
    // Filename click event handler
    if (fileUrl !== null && fileUrl !== '') {
      UploadFile.getFile(fileUrl).then(res => {
        console.log(`res: ${JSON.stringify(res)}`)
        window.open(res, '_blank')
      })
    }
  }
  const onContextMenuOpen = (e, item) => {
    // Show context menu
    contextMenu.value.show = true
    contextMenu.value.options.x = e.x
    contextMenu.value.options.y = e.y
    contextMenu.value.data = item
  }

  const deleteFile = () => {
    const file = deleteFileTemp.value
    console.log(`file. ${JSON.stringify(file)}`)
    if (file.s3url === null) {
      fileInfos.value = fileInfos.value.filter(item => {
        return item !== file
      })
    } else {
      const fItem = fileInfos.value.find(item => item === file)
      if (typeof fItem === 'undefined' || fItem === null) {
        if (
          typeof deleteFiles.value === 'undefined' ||
          deleteFiles.value === null
        ) {
          deleteFiles.value = []
        }
        deleteFiles.value.push(file)
      } else {
        fileInfos.value = fileInfos.value.filter(item => item !== file)
      }
    }
    fileDeleteModal.value = false
    deleteFileTemp.value = null
    onChange()
  }

  watch(
    () => props.items,
    (newVal, oldVal) => {
      console.log('items changed: ', newVal, ' | was: ', oldVal)

      if (newVal && oldVal && objectsAreIdentical(newVal, oldVal)) {
        return
      }

      if (typeof props.items !== 'undefined' && props.items !== null) {
        fileInfos.value = props.items.map((item, i) => {
          return {
            id: item.id || `local${i}`,
            fileName: item.fileName || Base.getFileName(item.s3url),
            file: null,
            s3url: item.s3url || null,
          }
        })
      }
    },
    {deep: true, immediate: true}
  )
</script>
<style type="text/css">
  .btn-disabled {
    opacity: 0.65;
    cursor: not-allowed !important;
    color: #fff;
    background-color: #636f83;
    border-color: #636f83;
  }
  .btn-disabled:hover {
    color: #fff;
    background-color: #636f83;
    border-color: #636f83;
    text-decoration: none;
  }
  .btn-disabled:focus {
    color: #fff !important;
    background-color: #636f83 !important;
    border-color: #636f83 !important;
    text-decoration: none;
  }
</style>
