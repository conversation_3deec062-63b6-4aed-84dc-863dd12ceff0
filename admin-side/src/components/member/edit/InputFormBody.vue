<script setup>
  import {defineProps} from 'vue';

  const props = defineProps({
    memberEditData: {
      type: Object,
      default: () => ({}),
    },
    countryOptions: {
      type: Array,
      default: () => [],
    },
    emailLangOptions: {
      type: Array,
      default: () => [],
    },
    handleCountryChange: {
      type: Function,
      default: () => {},
    },
    checkDateTimeValid: {
      type: Function,
      default: () => {},
    },
    getStatusName: {
      type: Function,
      default: () => {},
    },
    dateTimeValid: {
      type: Object,
      default: () => ({}),
    },
  });

  const currencyId = '円';
</script>

<template>
  <CForm onsubmit="return false;">
    <CRow class="mb-3">
      <CCol sm="3">
        <label>取引先コード</label>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="customerCode"
          v-model="memberEditData.freeField.customerCode"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>国</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormSelect
          name="country"
          :options="countryOptions"
          v-model="memberEditData.freeField.country"
          @update:modelValue="handleCountryChange"
        />
      </CCol>
    </CRow>
    <!-- 代表者氏名 -->
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>代表者氏名</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="ceo_name"
          v-model="memberEditData.freeField.ceoName"
        />
      </CCol>
    </CRow>
    <!-- 代表者氏名（カタカナ） -->
    <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>代表者氏名（カタカナ）</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="ceo_name_kana"
          v-model="memberEditData.freeField.ceoNameKana"
        />
      </CCol>
    </CRow>
    <!-- 代表者生年月日 -->
    <CRow v-if="memberEditData.freeField.country !== 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>代表者生年月日</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          type="date"
          :ref="'ceoBirthday'"
          horizontal
          v-model="memberEditData.freeField.ceoBirthday"
          :invalid="!dateTimeValid.ceoBirthday"
          @change="checkDateTimeValid('ceoBirthday', $event)"
        ></CFormInput>
      </CCol>
    </CRow>
    <!-- 会社名・貿易名 -->
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>会社名・貿易名</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="companyName"
          v-model="memberEditData.freeField.companyName"
        />
      </CCol>
    </CRow>
    <!-- 会社名・貿易名（カタカナ） -->
    <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>会社名・貿易名（カタカナ）</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="companyNameKana"
          v-model="memberEditData.freeField.companyNameKana"
        />
      </CCol>
    </CRow>
    <!-- 会社住所 -->
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>会社住所</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="company_address"
          v-model="memberEditData.freeField.companyAddress"
        />
      </CCol>
    </CRow>
    <!-- 会社設立日 -->
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>会社設立日</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          type="date"
          :ref="'establishmentDate'"
          horizontal
          v-model="memberEditData.freeField.establishmentDate"
          :invalid="!dateTimeValid.establishmentDate"
          @change="checkDateTimeValid('establishmentDate', $event)"
        ></CFormInput>
      </CCol>
    </CRow>
    <!-- 会社HP -->
    <CRow class="mb-3">
      <CCol sm="3">
        <label>会社HP</label>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="company_hp"
          v-model="memberEditData.freeField.companyHp"
        />
      </CCol>
    </CRow>
    <!-- 国コード -->
    <CRow v-if="memberEditData.freeField.country !== 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>国コード</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="country_code"
          v-model="memberEditData.freeField.telCountryCode"
        />
      </CCol>
    </CRow>
    <!-- 電話番号 -->
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>電話番号</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput name="tel" v-model="memberEditData.freeField.tel" />
      </CCol>
    </CRow>
    <!-- 事業内容 -->
    <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>事業内容</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="business_content"
          v-model="memberEditData.freeField.businessContent"
        />
      </CCol>
    </CRow>
    <!-- インボイス番号 -->
    <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>インボイス番号</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="invoice_no"
          v-model="memberEditData.freeField.invoiceNo"
        />
      </CCol>
    </CRow>
    <!-- 古物商許可証許可番号 -->
    <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>古物商許可証許可番号</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="antique_permit_no"
          v-model="memberEditData.freeField.antiquePermitNo"
        />
      </CCol>
    </CRow>
    <!-- 古物商許可証交付年月日 -->
    <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>古物商許可証交付年月日</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          type="date"
          :ref="'antiquePermitDate'"
          horizontal
          v-model="memberEditData.freeField.antiquePermitDate"
          :invalid="!dateTimeValid.antiquePermitDate"
          @change="checkDateTimeValid('antiquePermitDate', $event)"
        ></CFormInput>
      </CCol>
    </CRow>
    <!-- 古物商許可証公安委員会 -->
    <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>古物商許可証公安委員会</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="antique_permit_commission"
          v-model="memberEditData.freeField.antiquePermitCommission"
        />
      </CCol>
    </CRow>
    <!-- 担当者氏名 -->
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label v-if="memberEditData.freeField.country === 'JP'"
          >担当者名前</label
        >
        <label v-else>担当者氏名</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="member_name"
          v-model="memberEditData.freeField.memberName"
        />
      </CCol>
    </CRow>
    <!-- 担当者苗字 -->
    <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>担当者苗字</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="member_last_name"
          v-model="memberEditData.freeField.memberLastName"
        />
      </CCol>
    </CRow>
    <!-- メールアドレス -->
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>メールアドレス</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormInput name="email" v-model="memberEditData.freeField.email" />
      </CCol>
    </CRow>
    <!-- メール言語 -->
    <CRow class="mb-3">
      <CCol sm="3" class="d-flex align-items-start">
        <label>メール言語</label>
        <CBadge color="danger" class="ms-auto">必須</CBadge>
      </CCol>
      <CCol sm="3">
        <CFormSelect
          name="emailLang"
          v-model="memberEditData.freeField.emailLang"
        >
          <option
            v-for="option in emailLangOptions"
            :value="option.value"
            :key="option.value"
          >
            {{ option.title }}
          </option>
        </CFormSelect>
      </CCol>
    </CRow>
    <!-- WhatsApp -->
    <CRow v-if="memberEditData.freeField.country !== 'JP'" class="mb-3">
      <CCol sm="3">
        <label>WhatsApp</label>
      </CCol>
      <CCol sm="3">
        <CFormInput
          name="whatsApp"
          v-model="memberEditData.freeField.whatsApp"
        />
      </CCol>
    </CRow>
    <!-- WeChat -->
    <CRow v-if="memberEditData.freeField.country !== 'JP'" class="mb-3">
      <CCol sm="3">
        <label>WeChat</label>
      </CCol>
      <CCol sm="3">
        <CFormInput name="wechat" v-model="memberEditData.freeField.wechat" />
      </CCol>
    </CRow>

    <!-- 共通情報 -->
    <CRow class="mb-3">
      <CCol sm="3">
        <label>ステータス</label>
      </CCol>
      <CCol sm="2">
        {{ getStatusName(memberEditData.status) }}
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>入札</label>
      </CCol>
      <CCol sm="2" class="form-group col-sm-3">
        <CFormCheck
          name="bidAllowFlag"
          type="radio"
          inline
          v-for="option in [
            {value: '0', label: '否'},
            {value: '1', label: '可'},
          ]"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="memberEditData.status !== 1"
          :custom="true"
          v-model="memberEditData.bidAllowFlag"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>入札上限額</label>
      </CCol>
      <CCol sm="2">
        <CFormInput name="bidLimit" v-model="memberEditData.bidLimitAmount" />
      </CCol>
      <div class="col-auto">
        <div class="flex-row align-center">{{ currencyId }}</div>
      </div>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">
        <label>メール配信</label>
      </CCol>
      <CCol sm="2">
        <CFormCheck
          type="radio"
          inline
          v-for="option in [
            {value: '0', label: '配信しない'},
            {value: '1', label: '配信する'},
          ]"
          :key="option.value"
          :label="option.label"
          :value="option.value"
          :disabled="memberEditData.status !== 1"
          :custom="true"
          name="emailDeliveryFlag"
          v-model="memberEditData.emailDeliveryFlag"
        />
      </CCol>
    </CRow>
    <CRow class="mb-3">
      <CCol sm="3">備考</CCol>
      <CCol :sm="8">
        <CFormTextarea
          name="memo"
          v-model="memberEditData.freeField.memo"
          rows="3"
        />
      </CCol>
    </CRow>
  </CForm>
</template>
