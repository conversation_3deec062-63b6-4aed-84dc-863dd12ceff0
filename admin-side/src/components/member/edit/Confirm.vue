<script setup>
  import {dateFormat} from '@/views/common/commonFilters';
  import {defineProps, onMounted} from 'vue';

  const props = defineProps({
    countryOptions: {
      type: Array,
      default: () => [],
    },
    memberEditData: {
      type: Object,
      default: () => ({
        freeField: {},
      }),
    },
    emailLangOptions: {
      type: Array,
      default: () => [],
    },
    getStatusName: {
      type: Function,
      default: () => {},
    },
  });

  onMounted(() => {
    console.log(`memberEditData = ${JSON.stringify(props.memberEditData)}`);
  });

  const getCountryName = country => {
    const c = props.countryOptions.find(item => item.value === country)?.label;
    return c || '';
  };

  const getEmailLangName = emailLang => {
    const lang = props.emailLangOptions.find(
      item => item.value === emailLang
    )?.title;
    return lang || '';
  };
</script>
<template>
  <CCardBody>
    <CForm onsubmit="return false;">
      <CRow class="mb-3">
        <CCol sm="2">
          <label>取引先コード</label>
        </CCol>
        <CCol sm="2">
          {{ memberEditData.freeField.customerCode }}
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>国</label>
        </CCol>
        <CCol sm="4">
          {{ getCountryName(memberEditData.freeField.country) }}
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>代表者氏名</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.ceoName
          }}</label>
        </CCol>
      </CRow>
      <!-- 代表者氏名（カタカナ） -->
      <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
        <CCol sm="2">
          <label>代表者氏名（カタカナ）</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.ceoNameKana
          }}</label>
        </CCol>
      </CRow>
      <!-- 代表者生年月日 -->
      <CRow v-if="memberEditData.freeField.country !== 'JP'" class="mb-3">
        <CCol sm="2">
          <label>代表者生年月日</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            dateFormat(memberEditData.freeField.ceoBirthday)
          }}</label>
        </CCol>
      </CRow>
      <!-- 会社名・貿易名 -->
      <CRow class="mb-3">
        <CCol sm="2">
          <label>会社名・貿易名</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.companyName
          }}</label>
        </CCol>
      </CRow>
      <!-- 会社名・貿易名（カタカナ） -->
      <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
        <CCol sm="2">
          <label>会社名・貿易名（カタカナ）</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.companyNameKana
          }}</label>
        </CCol>
      </CRow>
      <!-- 会社住所 -->
      <CRow class="mb-3">
        <CCol sm="2">
          <label>会社住所</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.companyAddress
          }}</label>
        </CCol>
      </CRow>
      <!-- 会社設立日 -->
      <CRow class="mb-3">
        <CCol sm="2">
          <label>会社設立日</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            dateFormat(memberEditData.freeField.establishmentDate)
          }}</label>
        </CCol>
      </CRow>
      <!-- 会社HP -->
      <CRow class="mb-3">
        <CCol sm="2">
          <label>会社HP</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.companyHp
          }}</label>
        </CCol>
      </CRow>
      <!-- 国コード -->
      <CRow v-if="memberEditData.freeField.country !== 'JP'" class="mb-3">
        <CCol sm="2">
          <label>国コード</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.telCountryCode
          }}</label>
        </CCol>
      </CRow>
      <!-- 電話番号 -->
      <CRow class="mb-3">
        <CCol sm="2">
          <label>電話番号</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{ memberEditData.freeField.tel }}</label>
        </CCol>
      </CRow>
      <!-- 事業内容 -->
      <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
        <CCol sm="2">
          <label>事業内容</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.businessContent
          }}</label>
        </CCol>
      </CRow>
      <!-- インボイス番号 -->
      <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
        <CCol sm="2">
          <label>インボイス番号</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.invoiceNo
          }}</label>
        </CCol>
      </CRow>
      <!-- 古物商許可証許可番号 -->
      <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
        <CCol sm="2">
          <label>古物商許可証許可番号</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.antiquePermitNo
          }}</label>
        </CCol>
      </CRow>
      <!-- 古物商許可証交付年月日 -->
      <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
        <CCol sm="2">
          <label>古物商許可証交付年月日</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            dateFormat(memberEditData.freeField.antiquePermitDate)
          }}</label>
        </CCol>
      </CRow>
      <!-- 古物商許可証公安委員会 -->
      <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
        <CCol sm="2">
          <label>古物商許可証公安委員会</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.antiquePermitCommission
          }}</label>
        </CCol>
      </CRow>
      <!-- 担当者氏名 -->
      <CRow class="mb-3">
        <CCol sm="2">
          <label v-if="memberEditData.freeField.country === 'JP'"
            >担当者名前</label
          >
          <label v-else>担当者氏名</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.memberName
          }}</label>
        </CCol>
      </CRow>
      <!-- 担当者苗字 -->
      <CRow v-if="memberEditData.freeField.country === 'JP'" class="mb-3">
        <CCol sm="2">
          <label>担当者苗字</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.memberLastName
          }}</label>
        </CCol>
      </CRow>
      <!-- メールアドレス -->
      <CRow class="mb-3">
        <CCol sm="2">
          <label>メールアドレス</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{ memberEditData.freeField.email }}</label>
        </CCol>
      </CRow>
      <!-- メール言語 -->
      <CRow class="mb-3">
        <CCol sm="2">
          <label>メール言語</label>
        </CCol>
        <CCol sm="4">
          {{ getEmailLangName(memberEditData.freeField.emailLang) }}
        </CCol>
      </CRow>
      <!-- WhatsApp -->
      <CRow v-if="memberEditData.freeField.country !== 'JP'" class="mb-3">
        <CCol sm="2">
          <label>WhatsApp</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.whatsApp
          }}</label>
        </CCol>
      </CRow>
      <!-- WeChat -->
      <CRow v-if="memberEditData.freeField.country !== 'JP'" class="mb-3">
        <CCol sm="2">
          <label>WeChat</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break">{{
            memberEditData.freeField.wechat
          }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>ステータス</label>
        </CCol>
        <CCol sm="2">
          {{ getStatusName(memberEditData.status) }}
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>入札</label>
        </CCol>
        <CCol sm="2">
          <label class="word-break">{{
            memberEditData.bidAllowFlag === '1' ? '可' : '否'
          }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>入札上限額</label>
        </CCol>
        <CCol sm="4">
          <label class="word-break"
            >{{ memberEditData.bidLimitAmount }} 円</label
          >
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2">
          <label>メール配信</label>
        </CCol>
        <CCol sm="2">
          <label class="word-break">{{
            memberEditData.emailDeliveryFlag === '1' ? '配信する' : '配信しない'
          }}</label>
        </CCol>
      </CRow>
      <CRow class="mb-3">
        <CCol sm="2"> 備考 </CCol>
        <CCol sm="8">
          <p
            style="white-space: pre-line"
            v-html="memberEditData.freeField.memo"
          ></p>
        </CCol>
      </CRow>
    </CForm>
  </CCardBody>
</template>
<style type="text/css" scoped>
  .word-break {
    word-break: break-all;
  }
</style>
