class Validator {
  constructor(base) {
    this.base = base
  }

  validate(object, rules = Validator.loadRule()) {
    const {password, ...logObject} = Object.assign({}, object)
    console.log('パラメータログ：', logObject)

    const errList = Validator.checkRules(object, rules)
    return new Promise((resolve, reject) => {
      if (Object.keys(errList).length > 0) {
        return reject(this.createBaseErrorResponse(errList))
      }
      return resolve(object)
    })
  }

  createBaseErrorResponse(errorCodes) {
    const errors = this.convertErrorMessageWithBaseMassage(errorCodes)
    console.log('[TRACE LOG] error list of validator : ', errors)
    return {
      status: 400,
      errors,
    }
  }

  convertErrorMessageWithBaseMassage(errorCodes) {
    const define = this.base.define.message
    const errors = {}
    for (const key of Object.keys(errorCodes)) {
      if (typeof errorCodes[key] === 'object') {
        errors[key] = Validator.convertErrorCodeToErrorMessage(errorCodes[key])
      } else {
        errors[key] = define[errorCodes[key]] ?? errorCodes[key]
      }
    }
    // example output: { login_id: 'ログインIDは不正です。', password: 'パスワードは不正です。' }
    return errors
  }

  static loadRule() {
    try {
      return require('validation-rules')
    } catch (error) {
      return {}
    }
  }

  static loadMessage() {
    try {
      return require('./define/message')
    } catch (error) {
      console.log(error)
      return {}
    }
  }

  static checkLength(value, length) {
    if (value && value.length !== length) {
      return false
    }
    return true
  }

  static checkMaxLength(value, maxLength) {
    if (value && value.toString().length > maxLength) {
      return false
    }
    return true
  }

  static checkMinLength(value, minLength) {
    if (value && value.length < minLength) {
      return false
    }
    return true
  }

  static checkPattern(value, pattern) {
    if (
      typeof value === 'undefined' ||
      value === null ||
      value === '' ||
      pattern.test(value)
    ) {
      return true
    }
    return false
  }

  static checkNaturalNumber(value) {
    if (typeof value === 'undefined' || value === null || !isNaN(value)) {
      return true
    }
    return false
  }

  static checkTextNumber(value, length = null) {
    if (typeof value === 'string' && value.trim() === '' && value !== '') {
      return false
    }

    if (!Validator.checkRequired(value)) {
      return true
    }
    if (!isNaN(value) && (!length || Number(value) < 10 ** length)) {
      return true
    }
    return false
  }

  static checkRequired(value) {
    if (
      typeof value === 'undefined' ||
      value === null ||
      value === '' ||
      value === ' '
    ) {
      return false
    }

    if (
      typeof value === 'object' &&
      (value.length === 0 || Object.keys(value).length === 0)
    ) {
      return false
    }

    return true
  }

  static checkContain(value, containList) {
    for (const constain of containList) {
      if (String(value) === String(constain)) {
        return true
      }
    }
    return false
  }

  static checkDateFormat(value) {
    if (
      typeof value === 'undefined' ||
      value === null ||
      value === '' ||
      value === ' '
    ) {
      return true
    }

    const regex =
      /^[0-9]{4}\-(0[1-9]|1[0-2])\-(0[1-9]|[12][0-9]|3[01]) ([01][0-9]|2[0-3]):[0-5][0-9]$/
    if (!regex.test(value)) {
      return false
    }
    return true
  }

  static checkKataKana(value) {
    const regex = /^[\u30A0-\u30FF]+$/
    if (!regex.test(value)) {
      return false
    }
    return true
  }

  static isDateValid(inputDate) {
    // Check date validation
    // Format accepted: yyyy/MM/dd
    if (inputDate) {
      const regex = /^[0-9]{4}\/(0?[1-9]|1[0-2])\/(0?[1-9]|[12][0-9]|3[01])$/
      if (!regex.test(inputDate)) {
        return false
      }

      const s = inputDate.split('/')
      const tmpDate = new Date(inputDate)
      if (tmpDate instanceof Date && !isNaN(tmpDate)) {
        console.log('tmpDate: ', tmpDate)
        if (String(tmpDate.getMonth()) !== String(s[1] - 1)) {
          return false
        }
      } else {
        return false
      }
    }
    return true
  }

  static isDateTimeValid(inputDate) {
    // Check date validation
    // Format accepted: yyyy/MM/dd HH:mm
    if (inputDate) {
      const regex =
        /^[0-9]{4}\/(0?[1-9]|1[0-2])\/(0?[1-9]|[12][0-9]|3[01]) ([01]?[0-9]|2[0-3]):[0-5]?[0-9]$/
      if (!regex.test(inputDate)) {
        return false
      }

      const s = inputDate.split('/')
      const tmpDate = new Date(inputDate)
      if (tmpDate instanceof Date && !isNaN(tmpDate)) {
        console.log('tmpDate: ', tmpDate)
        if (String(tmpDate.getMonth()) !== String(s[1] - 1)) {
          return false
        }
      } else {
        return false
      }
    }
    return true
  }

  static checkListFormat(list) {
    if (!list || list === null || typeof list === 'undefined') {
      return true
    }

    if (typeof list !== 'object') {
      return false
    }

    if (Object.keys(list).filter(key => isNaN(key)).length > 0) {
      return false
    }

    return true
  }

  static checkListTextNumber(list) {
    if (!Validator.checkListFormat(list)) {
      return false
    }

    if (list.filter(x => !Validator.checkTextNumber(x)).length > 0) {
      return false
    }

    return true
  }

  static checkListValidation(list, elementRules) {
    const errList = []

    if (
      !list ||
      list === null ||
      typeof list === 'undefined' ||
      list.length === 0
    ) {
      return errList
    }

    for (const key of Object.keys(list)) {
      const element = list[key]
      const elementError = Validator.checkRules(element, elementRules)
      if (Object.keys(elementError).length > 0) {
        errList[key] = elementError
      }
    }

    return errList
  }

  /**
   * Validates an object against a set of validation rules and returns error codes for failed validations.
   * @param object
   * @param rules
   * @returns {object} An object containing validation errors where keys are field names and values are error codes.
   *                   Returns empty object {} if all validations pass.
   * @example
   * const object = { exhibition_no: '',quantity: 'abc' };
   * const rules = {
   *   exhibition_no: { REQUIRED_CHECK: true,REQUIRED_ERROR_MESSAGE: 'E000406' };
   *   quantity: { REQUIRED_CHECK: true,REQUIRED_ERROR_MESSAGE: 'E100400' };
   * };
   * Output: { exhibition_no: 'E000406', quantity: 'E100412' }
   */
  static checkRules(object, rules) {
    const errList = {}
    for (const key of Object.keys(rules)) {
      const checkBody = rules[key]
      const value = object[key]
      if (checkBody.REQUIRED_CHECK && !Validator.checkRequired(value)) {
        errList[key] = checkBody.REQUIRED_ERROR_MESSAGE
      } else if (checkBody.LIST_CHECK) {
        if (!Validator.checkListFormat(value)) {
          errList[key] = checkBody.LIST_CHECK_ERROR_MESSAGE
        }
        const error = Validator.checkListValidation(
          value,
          checkBody.ELEMENT_RULES
        )
        if (error.length > 0) {
          errList[key] = error
        }
      } else if (
        checkBody.PATTERN_CHECK &&
        !Validator.checkPattern(value, checkBody.PATTERN)
      ) {
        errList[key] = checkBody.PATTERN_ERROR_MESSAGE
      } else if (
        checkBody.LENGTH_CHECK &&
        !Validator.checkLength(value, checkBody.LENGTH)
      ) {
        errList[key] = checkBody.LENGTH_ERROR_MESSAGE
      } else if (
        checkBody.MAX_LENGTH_CHECK &&
        !Validator.checkMaxLength(value, checkBody.MAX_LENGTH)
      ) {
        errList[key] = checkBody.MAX_LENGTH_ERROR_MESSAGE
      } else if (
        checkBody.MIN_LENGTH_CHECK &&
        !Validator.checkMinLength(value, checkBody.MIN_LENGTH)
      ) {
        errList[key] = checkBody.MIN_LENGTH_ERROR_MESSAGE
      } else if (
        checkBody.CONTAIN_CHECK &&
        !Validator.checkContain(value, checkBody.CONTAIN_CHECK_LIST)
      ) {
        errList[key] = checkBody.CONTAIN_CHECK_ERROR_MESSAGE
      } else if (
        checkBody.NATURAL_NUMBER_CHECK &&
        !Validator.checkNaturalNumber(value)
      ) {
        errList[key] = checkBody.NATURAL_NUMBER_ERROR_MESSAGE
      } else if (
        checkBody.LIST_TEXT_NUMBER_CHECK &&
        !Validator.checkListTextNumber(value)
      ) {
        errList[key] = checkBody.LIST_TEXT_NUMBER_ERROR_MESSAGE
      } else if (
        checkBody.DATE_FORMAT_CHECK &&
        !Validator.checkDateFormat(value)
      ) {
        errList[key] = checkBody.DATE_FORMAT_ERROR_MESSAGE
      } else if (checkBody.KATAKANA_CHECK && !Validator.checkKataKana(value)) {
        errList[key] = checkBody.KATAKANA_ERROR_MESSAGE
      }
    }
    return errList
  }

  static validation(object, rules = Validator.loadRule()) {
    // ログ出力
    const {password, ...logObject} = {...object}
    console.log('パラメータログ：', logObject)

    const errList = Validator.checkRules(object, rules)
    return new Promise((resolve, reject) => {
      if (Object.keys(errList).length > 0) {
        return reject(Validator.createErrorResponse(errList))
      }
      return resolve(object)
    })
  }

  static convertErrorCodeToErrorMessage(errorCodes) {
    const define = Validator.loadMessage()
    const errors = {}
    for (const key of Object.keys(errorCodes)) {
      if (typeof errorCodes[key] === 'object') {
        errors[key] = Validator.convertErrorCodeToErrorMessage(errorCodes[key])
      } else {
        errors[key] = define[errorCodes[key]] ?? errorCodes[key]
      }
    }
    return errors
  }

  static createErrorResponse(errorCodes) {
    const errors = Validator.convertErrorCodeToErrorMessage(errorCodes)
    return {
      status: 400,
      errors,
    }
  }
}

module.exports = Validator
