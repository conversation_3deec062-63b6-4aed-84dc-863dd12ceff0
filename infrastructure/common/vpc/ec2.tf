resource "aws_subnet" "ec2_subnet" {
  vpc_id     = aws_vpc.main_vpc.id
  cidr_block = "10.0.0.0/24"

  tags = {
    Name = "${var.environment}-${var.project_name}-ec2-subnet"
  }
}

resource "aws_route_table" "ec2_route_table" {
  vpc_id = aws_vpc.main_vpc.id

  tags = {
    Name = "${var.environment}-${var.project_name}-ec2-route-table"
  }
}

resource "aws_route" "ec2_route_igw" {
  destination_cidr_block = "0.0.0.0/0"
  route_table_id         = aws_route_table.ec2_route_table.id
  gateway_id             = aws_internet_gateway.ec2_internet_gateway.id
}

resource "aws_route_table_association" "ec2_subnet_association_rtb" {
  subnet_id      = aws_subnet.ec2_subnet.id
  route_table_id = aws_route_table.ec2_route_table.id
}
