resource "aws_cloudwatch_metric_alarm" "rds-cpu-utilization-alarm" {
  count               = length(aws_rds_cluster_instance.aurora_cluster_instance)
  alarm_name          = "${var.environment}-${var.project_name}-rds-instance-${count.index}-cpu-utilization-alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  alarm_description   = "【${var.environment}-${var.project_name}-rds-instance-${count.index}】CPU使用率が「80%」を超えました"
  threshold           = 80.0
  treat_missing_data  = "missing"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  alarm_actions       = [var.slack-notification-sns-topic-arn]
  dimensions = {
    DBInstanceIdentifier = "${var.environment}-${var.project_name}-database-instance-${count.index}"
  }

}
