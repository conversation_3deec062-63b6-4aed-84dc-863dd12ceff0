variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "database_security_group_ids" {
  description = "database_security_group_ids"
}

variable "database_subnet_ids" {
  description = "database_subnet_ids"
}

variable "instance_class" {
  description = "instance_class"
}

variable "ca_cert_identifier" {
  description = "ca_cert_identifier"
  default = "rds-ca-ecc384-g1"
}

variable "family" {
  description = "family"
  default = "aurora-postgresql16"
}

variable "engine" {
  description = "engine"
  default = "aurora-postgresql"
}

variable "engine_version" {
  description = "engine_version"
  default = "16.4"
}

variable "backup_retention_period" {
  description = "backup_retention_period"
  default = 7
}

variable "preferred_backup_window" {
  description = "preferred_backup_window"
  default = "03:00-04:00"
}

variable "preferred_maintenance_window" {
  description = "preferred_maintenance_window"
  default = "tue:19:00-tue:20:00"
}

variable "database_name" {
  description = "database_name"
  default = "postgres"
}

variable "master_username" {
  description = "master_username"
  default = "postgres"
}

variable "port" {
  description = "database port"
  default = "5432"
}

variable "proxies_security_group_ids" {
  description = "proxies_security_group_ids"
}

variable "proxies_subnet_ids" {
  description = "proxies_security_group_ids"
}

variable "slack-notification-sns-topic-arn" {
  description = "slack-notification-sns-topic-arn"
}
