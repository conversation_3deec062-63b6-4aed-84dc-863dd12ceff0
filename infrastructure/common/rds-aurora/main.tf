resource "random_password" "password" {
  length = 16
  special = false
#   override_special = "_%@"
}

resource "aws_rds_cluster" "database_cluster" {
    cluster_identifier              = "${var.environment}-${var.project_name}-database-cluster"
    engine                          = var.engine
    engine_version                  = var.engine_version
    allow_major_version_upgrade     = true
    database_name                   = var.database_name
    master_username                 = var.master_username
    master_password                 = random_password.password.result
    port                            = var.port
    backup_retention_period         = var.backup_retention_period
    preferred_backup_window         = var.preferred_backup_window
    preferred_maintenance_window    = var.preferred_maintenance_window
    db_subnet_group_name            = aws_db_subnet_group.database_subnet_group.name
    final_snapshot_identifier       = "${var.environment}-${var.project_name}-database-cluster"
    vpc_security_group_ids          = var.database_security_group_ids
    db_cluster_parameter_group_name = aws_rds_cluster_parameter_group.cluster_parameter_group.id
    storage_encrypted               = true
    deletion_protection             = true
    apply_immediately               = true
    lifecycle {
        create_before_destroy = true
        ignore_changes = [ engine_version ]
    }
}

resource "aws_rds_cluster_instance" "aurora_cluster_instance" {
    count                   = length(var.database_subnet_ids)
    engine                  = var.engine
    engine_version          = var.engine_version
    identifier              = "${var.environment}-${var.project_name}-database-instance-${count.index}"
    cluster_identifier      = aws_rds_cluster.database_cluster.id
    instance_class          = var.instance_class
    db_subnet_group_name    = aws_db_subnet_group.database_subnet_group.name
    db_parameter_group_name = aws_db_parameter_group.parameter_group.id
    ca_cert_identifier      = var.ca_cert_identifier
    performance_insights_enabled    = true
    auto_minor_version_upgrade      = true
    monitoring_interval             = 60
    monitoring_role_arn             = aws_iam_role.enhanced_monitoring.arn
    lifecycle {
        create_before_destroy = true
        ignore_changes = [ engine_version ]
    }
}
