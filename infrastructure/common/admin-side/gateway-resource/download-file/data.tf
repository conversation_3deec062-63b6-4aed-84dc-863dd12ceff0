data "aws_iam_policy_document" "s3-policy" {

  statement {

    effect  = "Allow"
    actions = [
        "s3:getObject"
    ]

    resources = [
      "${var.s3-bucket-arn}/constant/*",
      "${var.s3-bucket-arn}/public/*",
      "${var.s3-bucket-arn}/notice/*",
      "${var.s3-bucket-arn}/exhibition-email/*",
      "${var.s3-bucket-arn}/member-request/*",
      "${var.s3-bucket-arn}/notice-email/*",
      "${var.s3-bucket-arn}/item-ancillary/*",
      "${var.s3-bucket-arn}/static-page/*",
    ]

  }
}
