const {cloneDeep} = require('lodash')
const validationRules = require('./validation-rules')
const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool()

exports.handle = (e, ctx, cb) => {
  console.log('upsert-item')
  const params = Base.parseRequestBody(e.body)
  let orig_item = null
  let auctionClassification = null

  const rules = cloneDeep(validationRules)

  ctx.callbackWaitsForEmptyEventLoop = false
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => {
      console.log('CHECK EXHIBITION STATUS')
      // Check exhibition status
      if (params.data && params.data.exhibition_no > 0) {
        const sqlParams = [Base.extractTenantId(e), params.data.exhibition_no]
        return pool
          .rlsQuery(
            Base.extractTenantId(e),
            Define.QUERY.GET_EXHIBITION_STATUS_FUNCTION,
            sqlParams
          )
          .then(result => {
            console.log(`result: ${JSON.stringify(result)}`)
            /*
             * 2022.08.01: 結果未確定じゃなくても変更できる
             * if (result && result.length > 0  && result[0].exhibition_status !== null && result[0].exhibition_status !== 0) {
             *   const err = {
             *     'exhibition_status' : 'E000608'
             *   }
             *   return Promise.reject(Validator.createErrorResponse(err))
             * }
             */

            // 終了時間
            if (result && result.length > 0) {
              auctionClassification =
                result[0].exhibition_classification_info?.auctionClassification
            }
            return Promise.resolve()
          })
      }
      const err = {
        exhibition_no: 'E000406',
      }
      return Promise.reject(Validator.createErrorResponse(err))
    })
    .then(() => {
      console.log('GET ORIGINAL ITEM INFO')

      console.log(`params.data.manage_no: ${params.data.manage_no}`)
      if (!Validator.checkRequired(params.data.manage_no)) {
        return Promise.resolve()
      }

      // Get item's current information
      const sql_params = [
        Base.extractTenantId(e),
        params.data.exhibition_no,
        params.data.manage_no,
        Base.extractAdminLanguageCode(e),
      ]
      return pool
        .rlsQuery(
          Base.extractTenantId(e),
          Define.QUERY.GET_ITEM_BY_MANAGE_NO,
          sql_params
        )
        .then(result => {
          console.log('result: ', result)
          if (
            typeof result !== 'undefined' &&
            result !== null &&
            result.length > 0
          ) {
            orig_item = result[0]
          }
          return Promise.resolve()
        })
    })
    .then(() => {
      console.log('Validation for common fields')
      const checkParam = Object.assign({}, params.data)

      // If the item with manage_no is already exist then check the status of the item
      if (orig_item) {
        // 直接成約されている商品(t_item.status = 3で)の場合はエラーにする
        // 「成約済みのため出品できません」
        if (orig_item.status === 3) {
          const err = {
            status: 400,
            message: Define.MESSAGE.E100405,
          }
          return Promise.reject(err)
        }
        if (
          orig_item.status === 2 &&
          ((checkParam.item_no &&
            String(orig_item.item_no) !== String(checkParam.item_no) &&
            String(orig_item.exhibition_no) ===
              String(checkParam.exhibition_no)) ||
            (!checkParam.item_no &&
              String(orig_item.exhibition_no) ===
                String(checkParam.exhibition_no)))
        ) {
          // 既に別の入札会に出品されている商品(t_item.status = 2で別の入札会に出品中
          // ※選択中の入札会に出品中の場合はエラーにしない)の場合はエラーメッセージを表示する
          // 「すでに別の入札会に出品されているため出品できません」
          const err = {
            status: 400,
            message: Define.MESSAGE.E100406,
          }
          return Promise.reject(err)
        }

        // 入札が既にある場合は開始価格を変更させない
        if (orig_item.bid_count > 0) {
          const errs = {}
          for (const key of [
            'lowest_bid_price',
            'lowest_bid_accept_price',
            'lowest_bid_quantity',
            'lowest_bid_accept_quantity',
            'quantity',
          ]) {
            console.log(`orig_item.${key}: `, orig_item[key])
            console.log(`checkParam.${key}: `, checkParam[key])
            if (orig_item[key] !== checkParam[key]) {
              const columnName = Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME[key]
              const msg = Common.format(Define.MESSAGE.E100409, [columnName])
              errs[key] = msg
            }
          }
          if (Object.keys(errs).length > 0) {
            return Promise.reject({
              status: 400,
              errors: {...errs},
            })
          }
        }
      }

      // Check valid quantity in case of seriage
      checkParam.quantity1Err = 0
      checkParam.lowestBidQty1Err = 0
      checkParam.lowestBidAcceptQty1Err = 0
      console.log('auctionClassification: ', auctionClassification)
      if (auctionClassification === 1) {
        rules.quantity.PATTERN = /^1$/
        rules.lowest_bid_quantity.PATTERN = /^1$/
        rules.lowest_bid_accept_quantity.PATTERN = /^1$/
        rules.quantity.PATTERN_ERROR_MESSAGE = 'E100416'
        rules.lowest_bid_quantity.PATTERN_ERROR_MESSAGE = 'E100416'
        rules.lowest_bid_accept_quantity.PATTERN_ERROR_MESSAGE = 'E100416'

        // if (!Common.isEmpty(checkParam.quantity) && checkParam.quantity !== 1) {
        //   checkParam.quantity1Err = -1
        // }
        // if (!Common.isEmpty(checkParam.lowest_bid_quantity) && checkParam.lowest_bid_quantity !== 1) {
        //   checkParam.lowestBidQty1Err = -1
        // }
        // if (!Common.isEmpty(checkParam.lowest_bid_accept_quantity) && checkParam.lowest_bid_accept_quantity !== 1) {
        //   checkParam.lowestBidAcceptQty1Err = -1
        // }
      } else {
        rules.quantity.PATTERN = /^[1-9][0-9]*$/
        rules.quantity.PATTERN_ERROR_MESSAGE = 'E100412'
        rules.lowest_bid_quantity.PATTERN = /^[1-9][0-9]*$/
        rules.lowest_bid_quantity.PATTERN_ERROR_MESSAGE = 'E100412'
        rules.lowest_bid_accept_quantity.PATTERN = /^[1-9][0-9]*$/
        rules.lowest_bid_accept_quantity.PATTERN_ERROR_MESSAGE = 'E100412'
      }

      // Check valid quantity
      checkParam.lowestBidQtyErr = 0
      checkParam.lowestBidAcceptQtyErr = 0
      checkParam.lowestBidVsAcceptQtyErr = 0
      console.log(
        'checkParam.lowest_bid_quantity > checkParam.quantity: ',
        checkParam.lowest_bid_quantity > checkParam.quantity
      )
      if (
        !Common.isEmpty(checkParam.lowest_bid_quantity) &&
        !Common.isEmpty(checkParam.quantity) &&
        checkParam.lowest_bid_quantity > checkParam.quantity
      ) {
        checkParam.lowestBidQtyErr = -1
      }
      if (
        !Common.isEmpty(checkParam.lowest_bid_accept_quantity) &&
        !Common.isEmpty(checkParam.quantity) &&
        checkParam.lowest_bid_accept_quantity > checkParam.quantity
      ) {
        checkParam.lowestBidAcceptQtyErr = -1
      }
      if (
        !Common.isEmpty(checkParam.lowest_bid_accept_quantity) &&
        !Common.isEmpty(checkParam.lowest_bid_quantity) &&
        checkParam.lowest_bid_accept_quantity < checkParam.lowest_bid_quantity
      ) {
        checkParam.lowestBidVsAcceptQtyErr = -1
      }
      // Check valid price
      checkParam.lowestBidAcceptPriceErr = 0
      if (
        !Common.isEmpty(checkParam.lowest_bid_accept_price) &&
        !Common.isEmpty(checkParam.lowest_bid_price) &&
        checkParam.lowest_bid_accept_price < checkParam.lowest_bid_price
      ) {
        checkParam.lowestBidAcceptPriceErr = -1
      }

      console.log('checkParam: ', checkParam)
      const errlist = Validator.checkRules(checkParam, rules)
      console.log('errlist : ', errlist)
      const resErrors = {}
      if (errlist && Object.keys(errlist).length > 0) {
        const errMgs = Validator.convertErrorCodeToErrorMessage(errlist)
        for (const key of Object.keys(errMgs)) {
          console.log('log:  errMgs -> key: ', key)
          let columnName1 = ''
          let columnName2 = ''

          switch (key) {
            case 'quantity1Err':
              columnName1 = Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME.quantity
              resErrors[key] = Common.format(errMgs[key], [columnName1])
              break
            case 'lowestBidQty1Err':
              columnName1 =
                Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME.lowest_bid_quantity
              resErrors[key] = Common.format(errMgs[key], [columnName1])
              break
            case 'lowestBidAcceptQty1Err':
              columnName1 =
                Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME
                  .lowest_bid_accept_quantity
              resErrors[key] = Common.format(errMgs[key], [columnName1])
              break
            case 'lowestBidQtyErr':
              columnName1 =
                Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME.lowest_bid_quantity
              columnName2 = Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME.quantity
              resErrors[key] = Common.format(errMgs[key], [
                columnName1,
                columnName2,
              ])
              break
            case 'lowestBidAcceptQtyErr':
              columnName1 =
                Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME
                  .lowest_bid_accept_quantity
              columnName2 = Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME.quantity
              resErrors[key] = Common.format(errMgs[key], [
                columnName1,
                columnName2,
              ])
              break
            case 'lowestBidVsAcceptQtyErr':
              columnName1 =
                Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME.lowest_bid_quantity
              columnName2 =
                Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME
                  .lowest_bid_accept_quantity
              resErrors[key] = Common.format(errMgs[key], [
                columnName1,
                columnName2,
              ])
              break
            case 'lowestBidAcceptPriceErr':
              columnName1 =
                Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME.lowest_bid_price
              columnName2 =
                Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME
                  .lowest_bid_accept_price
              resErrors[key] = Common.format(errMgs[key], [
                columnName1,
                columnName2,
              ])
              break
            default:
              columnName1 = Define.XLSX.IMPORT.EXHIBITION.COLUMN_NAME[key]
              resErrors[key] = Common.format(errMgs[key], [columnName1])
              break
          }
        }
      }
      if (resErrors && Object.keys(resErrors).length > 0) {
        return Promise.reject({
          status: 400,
          errors: resErrors,
        })
      }
      return Promise.resolve()
    })
    .then(() => {
      // Check for item fields
      console.log('Validation for customize item fields')
      if (!params.data || !params.data.localized_json_array) {
        return Promise.resolve()
      }
      const itemFields = params.data.localized_json_array
      console.log('itemFields: ', itemFields)
      if (!Array.isArray(itemFields) || itemFields.length === 0) {
        return Promise.resolve()
      }
      const sqlParams = [Base.extractTenantId(e), null]
      return pool
        .rlsQuery(
          Base.extractTenantId(e),
          Define.QUERY.GET_ITEM_FIELDS,
          sqlParams
        )
        .then(result => {
          console.log('result: ', result)
          if (!result || result.length === 0) {
            // No item fields defined
            return Promise.resolve()
          }
          // Check if the item_fields match the fields in the database
          return Promise.all(
            (result || []).map(lang => {
              console.log('lang: ', lang)
              const fieldRules = (lang.field_list || []).reduce((acc, x) => {
                acc[x.physical_name] = {
                  // Check required
                  REQUIRED_CHECK: x.required_flag === 1,
                  REQUIRED_ERROR_MESSAGE: Common.format(
                    Define.MESSAGE.E020000,
                    [x.logical_name]
                  ),
                  // Check max length
                  MAX_LENGTH_CHECK: x.max_length > 0,
                  MAX_LENGTH: x.max_length,
                  MAX_LENGTH_ERROR_MESSAGE: Common.format(
                    Define.MESSAGE.E020002,
                    [x.logical_name, x.max_length]
                  ),
                  // CHeck pattern
                  PATTERN_CHECK: x.data_type === 'reg' && x.regular_expressions,
                  PATTERN: new RegExp(x.regular_expressions),
                  PATTERN_ERROR_MESSAGE: Common.format(Define.MESSAGE.E020001, [
                    x.logical_name,
                  ]),
                }
                return acc
              }, {})
              console.log('fieldRules: ', fieldRules)
              const checkData =
                itemFields.find(z => z.language_code === lang.language_code)
                  ?.field_map || {}
              const errlist = Validator.checkRules(checkData, fieldRules)
              console.log('errlist: ', errlist)

              // Add language_code to the error keys (E.g., item_name_ja)
              if (errlist && Object.keys(errlist).length > 0) {
                const langErrs = Object.keys(errlist).reduce((acc, key) => {
                  const langKey = `${key}_${lang.language_code}`
                  acc[langKey] = errlist[key]
                  return acc
                }, {})
                console.log('langErrs: ', langErrs)
                return Promise.resolve(langErrs)
              }
              return Promise.resolve()
            })
          ).then(results => {
            console.log('checkAll: ', results)
            if (results && results.some(x => x && Object.keys(x).length > 0)) {
              const errs = results.reduce((acc, x) => {
                if (x && Object.keys(x).length > 0) {
                  Object.keys(x).forEach(key => {
                    acc[key] = x[key]
                  })
                }
                return acc
              }, {})
              return Promise.reject({
                status: 400,
                errors: errs,
              })
            }
            return Promise.resolve()
          })
        })
    })
    .then(() => {
      console.log('INSERT ITEM')
      const sql_params = [
        params.data,
        Base.extractTenantId(e),
        Base.extractAdminNo(e),
      ]
      return pool
        .rlsQuery(Base.extractTenantId(e), Define.QUERY.UPSERT_ITEM, sql_params)
        .then(result => {
          console.log('result = ', result)
          if (result && result.length > 0) {
            const itemNo = result[0].f_upsert_item
            return Promise.resolve(itemNo)
          }
          return Promise.reject({
            status: 400,
            message: Define.MESSAGE.E100411,
          })
        })
    })
    .then(item_no => {
      console.log('INSERT LOT')
      const item = params.data

      const sql_params = [
        item.exhibition_no,
        Base.extractTenantId(e),
        [item_no],
        item.lot_no,
        item.quantity,
        item.lowest_bid_quantity,
        item.lowest_bid_accept_quantity,
        item.lowest_bid_price,
        item.lowest_bid_accept_price,
        null,
        Base.extractAdminNo(e),
      ]
      return pool
        .rlsQuery(Base.extractTenantId(e), Define.QUERY.UPSERT_LOT, sql_params)
        .then(result => {
          console.log('result = ', JSON.stringify(result))
          return Promise.resolve(item_no)
        })
    })
    .then(item_no => {
      console.log('UPDATE ITEM IMAGES')
      // 画像
      const images = params.data.picturePath
      if (images) {
        const imageList = images.map(image => {
          return {
            division: 1,
            fileName: image.name,
            key: image.path,
          }
        })
        console.log('imageList: ', imageList)
        const sql_params = [
          Base.extractTenantId(e),
          item_no,
          params.data.manage_no,
          imageList,
        ]
        return pool
          .rlsQuery(
            Base.extractTenantId(e),
            Define.QUERY.INSERT_ITEM_FILES,
            sql_params
          )
          .then(result => {
            console.log('result = ', result)
            return Promise.resolve(imageList)
          })
      }
      return Promise.resolve([])
    })
    .then(imageList => {
      return Promise.all(
        imageList
          .filter(file => file.fileName.endsWith('.mp4'))
          .map(file => {
            return Common.invokeLambdaEventType(
              process.env.GET_FIRST_FRAME_MP4_LAMBDA_ARN,
              {
                key: file.key,
              },
              {maxRetries: 0}
            )
          })
      )
    })
    .then(() => {
      Base.createSuccessResponse(cb, [])
    })
    .catch(error => Base.createErrorResponse(cb, error))
}
