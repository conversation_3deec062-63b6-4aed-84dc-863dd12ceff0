module.exports = {
  exhibition_no: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E000406',
  },
  manage_no: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
  },
  quantity: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E100412',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 8,
    MAX_LENGTH_ERROR_MESSAGE: 'E000259',
  },
  quantity1Err: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E100416',
  },
  lowest_bid_quantity: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E100401',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E100412',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 8,
    MAX_LENGTH_ERROR_MESSAGE: 'E000255',
  },
  lowest_bid_price: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E100401',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E100412',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 8,
    MAX_LENGTH_ERROR_MESSAGE: 'E000256',
  },
  lowest_bid_accept_quantity: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E100401',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E100412',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 8,
    MAX_LENGTH_ERROR_MESSAGE: 'E000257',
  },
  lowest_bid_accept_price: {
    REQUIRED_CHECK: true,
    REQUIRED_ERROR_MESSAGE: 'E100400',
    NATURAL_NUMBER_CHECK: true,
    NATURAL_NUMBER_ERROR_MESSAGE: 'E100401',
    PATTERN_CHECK: true,
    PATTERN: /^[1-9][0-9]*$/,
    PATTERN_ERROR_MESSAGE: 'E100412',
    MAX_LENGTH_CHECK: true,
    MAX_LENGTH: 8,
    MAX_LENGTH_ERROR_MESSAGE: 'E000258',
  },
  lowestBidQtyErr: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E100413',
  },
  lowestBidAcceptQtyErr: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E100413',
  },
  lowestBidVsAcceptQtyErr: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E100413',
  },
  lowestBidAcceptPriceErr: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E100413',
  },
  lowestBidQty1Err: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E100416',
  },
  lowestBidAcceptQty1Err: {
    PATTERN_CHECK: true,
    PATTERN: /^[0-9]{1,}$/,
    PATTERN_ERROR_MESSAGE: 'E100416',
  },
}
