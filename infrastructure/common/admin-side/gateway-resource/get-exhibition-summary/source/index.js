const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body)
  ctx.callbackWaitsForEmptyEventLoop = false
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      const sqlParams = [Base.extractTenantId(e), params.exhibitionNo]
      return pool.rlsQuery(
        Base.extractTenantId(e),
        Define.QUERY.GET_EXHIBITION_SUMMARY_FUNCTION,
        sqlParams
      )
    })
    .then(res => {
      return Base.createSuccessResponse(cb, res)
    })
    .catch(error => Base.createErrorResponse(cb, error))
}
