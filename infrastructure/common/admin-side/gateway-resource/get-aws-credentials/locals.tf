locals {
  description = "S3にファイルアップロードする権限の一時的なAWS認証情報を取得する"
  environment_variables = {
    "TZ": "Asia/Tokyo",
    "AWS_CREDENTIALS_DURATION_SECONDS": "900",
    "S3_BUCKET": var.s3-bucket-id,
    "PUBLIC_FILE_UPLOAD": "public",
    "UPLOAD_PUBLIC_FILE_ROLE_ARN": module.upload-public-file-role.this_iam_role_arn,
    "CONSTANT_FILE_UPLOAD": "constant",
    "UPLOAD_CONSTANT_FILE_ROLE_ARN": module.upload-constant-file-role.this_iam_role_arn,
    "NOTICE_FILE_UPLOAD": "notice",
    "UPLOAD_NOTICE_FILE_ROLE_ARN": module.upload-notice-file-role.this_iam_role_arn,
    "EXHIBITION_EMAIL_FILE_UPLOAD": "exhibition-email",
    "UPLOAD_EXHIBITION_EMAIL_FILE_ROLE_ARN": module.upload-exhibition-email-file-role.this_iam_role_arn,
    "MEMBER_REQUEST_UPLOAD": "member-request",
    "UPLOAD_MEMBER_REQUEST_FILE_ROLE_ARN": module.upload-member-request-file-role.this_iam_role_arn,
    "CSV_FILE_UPLOAD": "csv-upload",
    "UPLOAD_CSV_FILE_ROLE_ARN": module.upload-csv-file-role.this_iam_role_arn,
    "NOTICE_EMAIL_FILE_UPLOAD": "notice-email",
    "UPLOAD_NOTICE_EMAIL_FILE_ROLE_ARN": module.upload-notice-email-file-role.this_iam_role_arn,
    "ITEM_ANCILLARY_UPLOAD": "item-ancillary",
    "UPLOAD_ITEM_ANCILLARY_FILE_ROLE_ARN": module.upload-item-ancillary-file-role.this_iam_role_arn,
    "STATIC_PAGE_FILE_UPLOAD": "static-page",
    "UPLOAD_STATIC_PAGE_FILE_ROLE_ARN": module.upload-static-page-file-role.this_iam_role_arn,
  }
}
