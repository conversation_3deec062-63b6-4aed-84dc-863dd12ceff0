module "upload-public-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-public-file-role"

  resources = ["${var.s3-bucket-arn}/public/*"]
}

module "upload-notice-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-notice-file-role"

  resources = ["${var.s3-bucket-arn}/notice/*"]
}

module "upload-constant-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-constant-file-role"

  resources = ["${var.s3-bucket-arn}/constant/*"]
}

module "upload-exhibition-email-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-exhibition-email-file-role"

  resources = ["${var.s3-bucket-arn}/exhibition-email/*"]
}

module "upload-member-request-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-member-request-file-role"

  resources = ["${var.s3-bucket-arn}/member-request/*"]
}

module "upload-csv-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-csv-file-role"

  resources = ["${var.s3-bucket-arn}/csv-upload/*"]
}

module "upload-notice-email-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-notice-email-file-role"

  resources = ["${var.s3-bucket-arn}/notice-email/*"]
}

module "upload-item-ancillary-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-item-ancillary-file-role"

  resources = ["${var.s3-bucket-arn}/item-ancillary/*"]
}

module "upload-static-page-file-role" {
  source = "../../../../modules/iam-assumable-s3-upload-role"

  trusted_role_arns = [
    module.gateway-resource.lambda_iam_role_arn
  ]

  role_name = "${var.environment}-${var.project_name}-${var.prefix_function_name}-upload-static-page-file-role"

  resources = ["${var.s3-bucket-arn}/static-page/*"]
}
