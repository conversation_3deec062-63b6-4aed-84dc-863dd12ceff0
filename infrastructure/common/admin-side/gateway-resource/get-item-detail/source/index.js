const Define = require(`${process.env.COMMON_LAYER_PATH}define`)
const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const pool = new PgPool(process.env.READ_ONLY_PGHOST)

exports.handle = (e, ctx, cb) => {
  const params = Base.parseRequestBody(e.body)
  ctx.callbackWaitsForEmptyEventLoop = false
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        Base.extractTenantId(e)
      )
    )
    .then(() => Validator.validation(params))
    .then(() => {
      const sqlParams = [
        params.item_no, // in_item_no
        Base.extractTenantId(e), // in_tenant_no
        null, // Base.extractAdminLanguageCode(e), // in_language_code
      ]
      return pool.rlsQuery(
        Base.extractTenantId(e),
        Define.QUERY.GET_ITEM_DETAIL,
        sqlParams
      )
    })
    .then(result => Base.createSuccessResponse(cb, result))
    .catch(error => Base.createErrorResponse(cb, error))
}
