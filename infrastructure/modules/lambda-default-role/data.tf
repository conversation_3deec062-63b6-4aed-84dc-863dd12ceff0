data "aws_caller_identity" "caller-current" {}

data "aws_iam_policy_document" "lambda-trust-rel-policy" {

  statement {

    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }

  }

}

data "aws_iam_policy_document" "logs_policy" {

  statement {

    effect = "Allow"

    actions = [
      "logs:*"
    ]

    resources = ["*"]

  }

}

data "aws_iam_policy_document" "call-slack-notification-policy" {
  count = var.slack-notification-lambda-arn != "" ? 1 : 0

  statement {

    effect  = "Allow"
    actions = [
        "lambda:InvokeFunction"
    ]

    resources = [var.slack-notification-lambda-arn]
  }

}
