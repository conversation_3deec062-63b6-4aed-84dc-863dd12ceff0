resource "aws_iam_role" "aws_iam_role" {
  name = var.lambda_function_role_name

  assume_role_policy = data.aws_iam_policy_document.lambda-trust-rel-policy.json
}

resource "aws_iam_role_policy" "logs-policy" {
  name = "${var.lambda_function_role_name}-logs-policy"
  role = aws_iam_role.aws_iam_role.id

  policy = data.aws_iam_policy_document.logs_policy.json
}

resource "aws_iam_role_policy_attachment" "vpc-policy" {
  role = aws_iam_role.aws_iam_role.id
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy" "call-slack-notification-policy" {
  count = var.slack-notification-lambda-arn != "" ? 1 : 0
  name = "${var.lambda_function_role_name}-call-slack-notification-policy"
  role = aws_iam_role.aws_iam_role.id

  policy = data.aws_iam_policy_document.call-slack-notification-policy[0].json
}
