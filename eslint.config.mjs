import pluginJs from '@eslint/js'
import jsdoc from 'eslint-plugin-jsdoc'
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended'
import pluginVue from 'eslint-plugin-vue'
import globals from 'globals'

export default [
  {
    files: ['**/*.{js,mjs,cjs,vue}'],
    plugins: {
      jsdoc,
    },
  },
  {
    ignores: [
      'node_modules',
      'dist',
      'auction-side/public/js/*',
      'tools/sql_out',
    ],
  },
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
  },
  pluginJs.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  jsdoc.configs['flat/recommended'],
  eslintPluginPrettierRecommended,
  // commonRules,
  {
    rules: {
      'linebreak-style': 0,
      'multiline-comment-style': 0,
      'capitalized-comments': 0,
      'require-jsdoc': 0,
    },
  },
]
