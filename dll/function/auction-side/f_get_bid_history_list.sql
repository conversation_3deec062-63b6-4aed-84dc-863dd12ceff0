CREATE OR REPLACE FUNCTION public.f_get_bid_history_list
(
    in_tenant_no bigint,
    in_exhibition_item_nos bigint[]
)
RETURNS TABLE(
    exhibition_item_no bigint,
    bid_histories jsonb
)

LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 入札履歴を取得する
-- Parameters
-- @param in_exhibition_item_no 出品番号
----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  SELECT TBH.exhibition_item_no,
        jsonb_agg(
            jsonb_build_object(
                'nickname', TBH.bid_nickname,
                -- 入札時点のニックネーム
                'bid_price', TBH.bid_price,
                'after_current_price', TBH.after_current_price,
                'create_datetime', TBH.create_datetime
            )
            ORDER BY TBH.bid_price DESC, TBH.create_datetime ASC
        ) AS bid_histories
    FROM (
        SELECT *
        FROM (
            SELECT TBH.exhibition_item_no,
              TBH.bid_nickname,
              TBH.bid_price,
              TBH.after_current_price,
              TBH.create_datetime,
              ROW_NUMBER() OVER (
                PARTITION BY TBH.bid_nickname
                ORDER BY TBH.create_datetime DESC
              ) AS row_number
            FROM t_bid_history TBH
            LEFT JOIN t_bid TB
              ON TB.exhibition_item_no = TBH.exhibition_item_no
              AND TB.member_no = TBH.member_no
            WHERE TBH.exhibition_item_no = ANY(in_exhibition_item_nos)
              AND TB.bid_price IS NOT NULL
              AND TB.bid_price <> 0
            ORDER BY TBH.exhibition_item_no,
                    TBH.bid_nickname,
                    TBH.create_datetime DESC
          ) BH
        WHERE BH.row_number = 1
      ) TBH
    GROUP BY TBH.exhibition_item_no;
END;

$BODY$;
