CREATE OR REPLACE FUNCTION public.f_get_bid_payment (
    in_exhibition_item_no bigint,
    in_member_no bigint
)
RETURNS TABLE(
    exhibition_result_no bigint,
    manage_no character varying,
    item_field jsonb,
    bid_success_price numeric,
    tax_rate numeric,
    options jsonb[],
    payment_flag integer,
    payment_field jsonb,
    order_no character varying,
    create_datetime timestamp with time zone,
    member_field jsonb
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 落札情報取得
-- Parameters
-- @param in_exhibition_item_no 出品番号
-- @param in_member_no 会員番号
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TER.exhibition_result_no,
         TER.manage_no,
         TER.item_field,
         TER.bid_success_price,
         TER.tax_rate,
         TER.options,
         TER.payment_flag,
         TER.payment_field,
         TER.order_no,
         TER.create_datetime,
         MM.free_field as member_field
    FROM t_exhibition_result TER
    LEFT JOIN m_member MM
           ON MM.member_no = TER.bid_success_member_no
    WHERE TER.exhibition_item_no = in_exhibition_item_no
      AND TER.bid_success_member_no = in_member_no;
END;

$BODY$;
