CREATE OR REPLACE FUNCTION public.f_get_delivery_message (
    in_tenant_no bigint,
    in_member_no bigint,
    in_exhibition_item_no bigint
)
RETURNS TABLE(
    delivery_message_no bigint,
    update_category_id character varying,
    answer_delivery_message_no bigint,
    message character varying,
    member_no bigint,
    member_nickname text,
    checked_admin_no bigint,
    delete_flag integer,
    no_answer_flag integer,
    message_flag boolean
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 配送に関するお問い合わせ取得
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT TDM.delivery_message_no,
         TDM.update_category_id,
         TDM.answer_delivery_message_no,
         TDM.message,
         TDM.member_no,
         MM.free_field->>'nickname' member_nickname,
         TDM.checked_admin_no,
         TDM.delete_flag,
         TDM.no_answer_flag,
         CASE WHEN (in_member_no IS NULL OR TDM.member_no = in_member_no) THEN TRUE ELSE FALSE END AS message_flag
    FROM t_delivery_message TDM
    LEFT OUTER JOIN m_member MM
	    ON MM.member_no = TDM.member_no
     AND MM.tenant_no = in_tenant_no
    LEFT OUTER JOIN m_admin MA
	    ON MA.admin_no = TDM.checked_admin_no
     AND MA.tenant_no = in_tenant_no
   WHERE TDM.tenant_no = in_tenant_no
     AND TDM.exhibition_item_no = in_exhibition_item_no
     AND ((TDM.checked_admin_no IS NOT NULL AND TDM.delete_flag = 0 AND TDM.no_answer_flag = 0) OR TDM.create_admin_no IS NOT NULL OR TDM.member_no = in_member_no)
   ORDER BY COALESCE(TDM.answer_delivery_message_no, TDM.delivery_message_no), TDM.create_datetime;

END;

$BODY$;
