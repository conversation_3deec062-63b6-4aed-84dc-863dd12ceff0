CREATE OR REPLACE FUNCTION public.f_get_all_successful_bid_history(
    in_tenant_no bigint,
    in_member_no bigint,
    in_auction_classification_list integer[],
		in_search_keys character varying[],
  	in_categories character varying[],
    in_start_date character varying,
    in_end_date character varying,
    in_lang_code character varying,
		in_limit numeric
)
RETURNS TABLE(data jsonb)
LANGUAGE plpgsql
COST 100
VOLATILE PARALLEL UNSAFE
ROWS 1000

AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- ずべての落札履歴を取得する
-- Parameters
-- @param in_tenant_no テナント番号
-- @params in_member_no 会員番号
-- @params in_auction_classification_list オークション方式配列
-- @params in_start_date 開始日
-- @params in_end_date 終了日
-- @param in_lang_code 言語区分
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH
	escapedKeywords AS (
    SELECT f_escape_string(keyword) AS search_keys FROM unnest(in_search_keys) AS keyword
  ),
	bid_histories AS (
		SELECT ti.manage_no
		, te.exhibition_no
		, tel.exhibition_name
		, tei.exhibition_item_no
		, ti.area_id
		, te.start_datetime
		, te.end_datetime
		, te.exhibition_classification_info->>'auctionClassification' AS auction_classification
		, tei.start_datetime AS item_start_datetime
		, tei.end_datetime AS item_end_datetime
		, mcl.value2 auction_classification_name
		, tei.lot_no
		, tl.lot_id
		, ti.item_no
		, til.free_field
		, tiaf.file_path
		, terd.bid_success_price
		, tld.order_no
		, ROW_NUMBER() OVER (
			PARTITION BY te.exhibition_no
		 	ORDER BY terd.bid_success_price DESC, ti.manage_no ASC
		) AS row_number
		FROM t_exhibition te
						INNER JOIN t_exhibition_item tei
										ON te.exhibition_no = tei.exhibition_no
										-- AND top_member_no = in_member_no
										AND hummer_flag = 1
						INNER JOIN t_lot tl
										ON tei.lot_no = tl.lot_no
						INNER JOIN t_lot_detail tld
										ON tei.lot_no = tld.lot_no
						INNER JOIN t_item ti
										ON tld.item_no = ti.item_no
						INNER JOIN t_item_localized til
										ON ti.item_no = til.item_no
										AND til.language_code = in_lang_code
						LEFT JOIN t_exhibition_localized tel
										ON te.exhibition_no = tel.exhibition_no
										AND tel.language_code = in_lang_code
		LEFT OUTER JOIN (
		SELECT mcl.value1
						, mcl.value2
						FROM m_constant mc
						INNER JOIN m_constant_localized mcl
										ON mcl.constant_no = mc.constant_no
						WHERE mc.tenant_no = in_tenant_no
						AND mcl.language_code = in_lang_code
						AND mc.key_string = 'AUCTION_CLASSIFICATION'
						AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
										AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
		) mcl
										ON mcl.value1 = te.exhibition_classification_info->>'auctionClassification' :: character varying
		LEFT OUTER JOIN t_item_ancillary_file tiaf
										ON ti.manage_no = tiaf.manage_no
										AND tiaf.division = 1
										AND tiaf.item_no = ti.item_no
										AND tiaf.serial_number = 1
										AND tiaf.delete_flag = 0
		LEFT OUTER JOIN t_exhibition_result ter
										ON ter.exhibition_item_no = tei.exhibition_item_no
		LEFT OUTER JOIN (
			SELECT terd.exhibition_result_no,
							MAX(terd.bid_success_price) AS bid_success_price
			FROM t_exhibition_result_detail terd
			GROUP BY terd.exhibition_result_no
		) terd
										ON terd.exhibition_result_no = ter.exhibition_result_no

		WHERE
			te.tenant_no = in_tenant_no
			AND terd.exhibition_result_no IS NOT NULL -- 落札結果がある
			AND te.status = 1
			AND (te.exhibition_classification_info->>'auctionClassification') :: integer = ANY(in_auction_classification_list)
			AND to_timestamp(in_start_date, 'yyyy/MM/dd HH24:MI:SS') <= te.end_datetime
			AND to_timestamp(in_end_date, 'yyyy/MM/dd HH24:MI:SS') + '1 day' > te.end_datetime
			AND (
					in_search_keys IS NULL
					OR ARRAY_LENGTH(in_search_keys, 1) IS NULL
					OR (
						((til.free_field->>'product_name')::VARCHAR ilike (SELECT CONCAT('%' || LOWER(EK.search_keys) || '%') from escapedKeywords EK))
						OR ((til.free_field->>'maker')::VARCHAR ilike (SELECT CONCAT('%' || LOWER(EK.search_keys) || '%') from escapedKeywords EK))
					)
				)
			AND(
					ARRAY_LENGTH(in_categories, 1) IS NULL
					OR til.free_field->>'category' =  ANY(in_categories)
				)

		ORDER BY
		terd.bid_success_price DESC,
		ti.manage_no ASC
	),
	exhibition_group AS (
    SELECT jsonb_agg(json_build_object(
							 'exhibition_no', BHC.exhibition_no,
							 'start_datetime', BHC.start_datetime,
							 'end_datetime', BHC.end_datetime,
							 'exhibition_name', BHC.exhibition_name,
							 'count', BHC.count,
            	'auction_classification', BHC.auction_classification
					 )
					 ORDER BY BHC.end_datetime DESC
					 ) AS exhs
      FROM (
				SELECT BH.exhibition_no,
								BH.exhibition_name,
								BH.start_datetime,
								BH.end_datetime,
								COUNT(*) as count,
             		BH.auction_classification
				FROM bid_histories BH
				GROUP BY BH.exhibition_no, BH.exhibition_name, BH.start_datetime, BH.end_datetime,BH.auction_classification
			) BHC
	),
	count_all AS (
		SELECT COUNT(*) AS count
		FROM bid_histories
	),
	agg_items AS (
		SELECT jsonb_agg(json_build_object(
							'manage_no', BH.manage_no,
							'exhibition_no', BH.exhibition_no,
							'exhibition_name', BH.exhibition_name,
							'exhibition_item_no', BH.exhibition_item_no,
							'area_id', BH.area_id,
							'start_datetime', BH.item_start_datetime,
							'end_datetime', BH.item_end_datetime,
							'auction_classification_name', BH.auction_classification_name,
							'lot_no', BH.lot_no,
							'lot_id', BH.lot_id,
							'item_no', BH.item_no,
							'free_field', BH.free_field,
							'file_path', BH.file_path,
							'bid_success_price', BH.bid_success_price,
							'order_no', BH.order_no
						)) AS items
		FROM bid_histories BH
		WHERE BH.row_number <= in_limit
	)

  SELECT jsonb_build_object(
            'items', IT.items,
            'count', CA.count,
            'exhibition_group', EG.exhs
          )
  FROM agg_items IT
	CROSS JOIN count_all CA
  CROSS JOIN exhibition_group EG
	;

END;

$BODY$;
