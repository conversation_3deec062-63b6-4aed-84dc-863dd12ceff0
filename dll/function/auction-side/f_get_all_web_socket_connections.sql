CREATE OR <PERSON>EPLACE FUNCTION public.f_get_all_web_socket_connections (
    in_tenant_no bigint
)
RETURNS TABLE(
    connection_id character varying,
    tenant_no bigint,
    member_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
/************************************************************************/
--  処理内容： WebSocket接続取得
/************************************************************************/
DECLARE
BEGIN
    RETURN QUERY
    SELECT TWSC.connection_id,
           TWSC.tenant_no,
           TWSC.member_no
     FROM t_web_socket_connection TWSC
    WHERE TWSC.tenant_no = in_tenant_no;

END;

$BODY$;
