CREATE OR REPLACE FUNCTION public.f_get_current_price
(
    in_exhibition_item_no bigint
)
RETURNS TABLE(
    top_member_no bigint,
    current_price numeric,
    top_price numeric
)

LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE
----------------------------------------------------------------------------------------------------
-- 該当出品データのトップ会員、現在価格
-- Parameters
-- @param in_exhibition_item_no 出品番号
----------------------------------------------------------------------------------------------------

BEGIN
  RETURN QUERY
  WITH exhibition as (
    SELECT CASE WHEN TE.pitch_option = 1 THEN COALESCE(TEI.pitch_width, 0) ELSE COALESCE(NULLIF(AUTO_PITCH.value1, '')::numeric, 0) END AS pitch_width,
           TE.exhibition_classification_info->'bidCommitClassification' AS ext_bid_commit_classification,
           TEI.lowest_bid_accept_price
      FROM t_exhibition_item TEI
      LEFT JOIN t_exhibition TE
        ON TE.exhibition_no = TEI.exhibition_no
      LEFT JOIN (
        SELECT MAX(value1) AS value1, MAX(value2) AS value2, value3, value4
          FROM m_constant_localized
        WHERE constant_no IN (SELECT constant_no FROM m_constant WHERE key_string = 'PITCH_FOLLOW_BID_PRICE')
          AND language_code = 'ja'
        GROUP BY value3, value4
      ) AUTO_PITCH ON (AUTO_PITCH.value3 IS NULL OR LENGTH(AUTO_PITCH.value3) = 0 OR (f_isnumeric(AUTO_PITCH.value3) AND COALESCE(TEI.current_price, COALESCE(TEI.lowest_bid_price, 0)) >= COALESCE(NULLIF(AUTO_PITCH.value3, '')::numeric, 0)))
                  AND (AUTO_PITCH.value4 IS NULL OR LENGTH(AUTO_PITCH.value4) = 0 OR (f_isnumeric(AUTO_PITCH.value4) AND COALESCE(TEI.current_price, COALESCE(TEI.lowest_bid_price, 0)) < COALESCE(NULLIF(AUTO_PITCH.value4, '')::numeric, 0)))

     WHERE TEI.exhibition_item_no = in_exhibition_item_no
  ),
  top_bid as (
    SELECT  TB.member_no AS top_member_no,
            TB.bid_price AS top_price
      FROM t_bid TB
      LEFT JOIN t_exhibition_item TEI
        ON TB.exhibition_item_no = TEI.exhibition_item_no
     WHERE TB.exhibition_item_no = in_exhibition_item_no
       AND TB.bid_price >= TEI.lowest_bid_price
     ORDER BY TB.bid_price DESC, TB.update_datetime
     LIMIT 1
  ),
  second_bid as (
    SELECT MAX(TB.bid_price) AS bid_price
      FROM t_bid TB
      LEFT JOIN t_exhibition_item TEI
        ON TB.exhibition_item_no = TEI.exhibition_item_no
     CROSS JOIN top_bid
     WHERE TB.exhibition_item_no = $1
       AND TB.member_no <> COALESCE(top_bid.top_member_no, -1)
       AND TB.bid_price <> 0
  ),
  second_bid_price_pitch_width AS (
    SELECT SB.bid_price,
           CASE WHEN TE.pitch_option = 1 THEN COALESCE(TEI.pitch_width, 0) ELSE COALESCE(NULLIF(AUTO_PITCH.value1, '')::numeric, 0) END AS pitch_width
    FROM second_bid SB
    LEFT JOIN t_exhibition_item TEI
      ON TEI.exhibition_item_no = in_exhibition_item_no
    LEFT JOIN t_exhibition TE
      ON TE.exhibition_no = TEI.exhibition_no
    LEFT JOIN (
        SELECT MAX(value1) AS value1, MAX(value2) AS value2, value3, value4
          FROM m_constant_localized
        WHERE constant_no IN (SELECT constant_no FROM m_constant WHERE key_string = 'PITCH_FOLLOW_BID_PRICE')
          AND language_code = 'ja'
        GROUP BY value3, value4
      ) AUTO_PITCH ON (AUTO_PITCH.value3 IS NULL OR LENGTH(AUTO_PITCH.value3) = 0 OR (f_isnumeric(AUTO_PITCH.value3) AND COALESCE(SB.bid_price, 0) >= COALESCE(NULLIF(AUTO_PITCH.value3, '')::numeric, 0)))
                  AND (AUTO_PITCH.value4 IS NULL OR LENGTH(AUTO_PITCH.value4) = 0 OR (f_isnumeric(AUTO_PITCH.value4) AND COALESCE(SB.bid_price, 0) < COALESCE(NULLIF(AUTO_PITCH.value4, '')::numeric, 0)))
    WHERE SB.bid_price IS NOT NULL
      AND TEI.exhibition_item_no IS NOT NULL
      AND TE.exhibition_no IS NOT NULL
  )

  SELECT top_bid.top_member_no,
         CASE WHEN exhibition.ext_bid_commit_classification = '2' AND top_bid.top_price > exhibition.lowest_bid_accept_price
              THEN LEAST(
                     GREATEST(
                       COALESCE(second_bid.bid_price, 0) + COALESCE(SCPW.pitch_width, exhibition.pitch_width),
                       exhibition.lowest_bid_accept_price
                     ),
                     top_bid.top_price
                   )
              ELSE top_bid.top_price
         END AS current_price,
         top_bid.top_price
    FROM exhibition
    LEFT JOIN second_bid_price_pitch_width SCPW ON SCPW.bid_price IS NOT NULL
   CROSS JOIN top_bid
   CROSS JOIN second_bid

  ;

END;

$BODY$;
