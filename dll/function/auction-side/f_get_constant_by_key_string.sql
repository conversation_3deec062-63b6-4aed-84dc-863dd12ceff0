CREATE OR REPLACE FUNCTION public.f_get_constant_by_key_string (
    in_tenant_no bigint,
    in_key_string_list character varying[],
    in_lang_code character varying DEFAULT NULL
)
RETURNS TABLE(
    key_string character varying,
    value_name character varying,
    sort_order integer,
    start_datetime character varying,
    end_datetime character varying,
    value1 character varying,
    value2 character varying,
    value3 character varying,
    value4 character varying,
    value5 character varying,
    file_url character varying,
    language_code character varying
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 定数リストを取得する
-- Parameters
-- @param tenant_no character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT MC.key_string AS key_string
        ,MC.value_name AS value_name
        ,MC.sort_order AS sort_order
        ,TO_CHAR(MC.start_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying  AS start_datetime
        ,TO_CHAR(MC.end_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying  AS end_datetime
        ,MCL.value1
        ,MCL.value2
        ,MCL.value3
        ,MCL.value4
        ,MCL.value5
        ,MCL.file_url
        ,MCL.language_code
    FROM m_constant MC
    JOIN m_constant_localized MCL
      ON MCL.constant_no = MC.constant_no
  WHERE MC.tenant_no = in_tenant_no
    AND (in_lang_code IS NULL OR MCL.language_code = in_lang_code)
    AND MC.key_string = ANY(in_key_string_list)
    AND (now() BETWEEN COALESCE(MC.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                      AND COALESCE(MC.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
  ORDER BY MC.constant_no, MC.sort_order;

END;
$BODY$;
