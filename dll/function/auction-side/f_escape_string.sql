CREATE OR REPLACE FUNCTION public.f_escape_string (
    in_search_key character varying
)
RETURNS character varying
LANGUAGE plpgsql
COST 100
VOLATILE
AS $BODY$

DECLARE
  escaped_search_key character varying;
----------------------------------------------------------------------------------------------------
-- Escape string
----------------------------------------------------------------------------------------------------

BEGIN

  SELECT REGEXP_REPLACE(in_search_key, '([%\.\\\+\*\?\[\^\]\$\(\)\{\}\=\!\<\>\|\:\-])', '\\\&', 'g') INTO escaped_search_key;

  RETURN escaped_search_key;

END;
$BODY$;
