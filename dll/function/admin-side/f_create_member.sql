CREATE OR REPLACE FUNCTION public.f_create_member (
    in_tenant_no bigint,
    in_free_field jsonb,
    in_bid_allow_flag integer,
    in_email_delivery_flag integer,
    in_temporary_password character varying,
    in_require_password_change integer,
    in_require_confirm_token integer,
    in_token_string character varying,
    in_token_expire timestamp without time zone,
    in_update_admin_no bigint
)
RETURNS bigint
LANGUAGE plpgsql

COST 100
VOLATILE
AS $BODY$

DECLARE
  return_member_no bigint;

----------------------------------------------------------------------------------------------------
-- 会員登録
----------------------------------------------------------------------------------------------------

BEGIN

  WITH
  -- 会員申請
  create_member_request AS (
    INSERT INTO t_member_request (
      tenant_no,
      member_request_type,
      free_field,
      update_admin_no,
      delete_flag
    )
    VALUES (
      in_tenant_no,
      1,
      in_free_field,
      in_update_admin_no,
      1 -- すぐに削除
    )
    RETURNING
      member_request_no,
      email_priority,
      classification,
      free_field
  ),
  -- member_noを取得する
  get_member_no AS (
    SELECT nextval('m_member_no_seq'::regclass) as member_no
  ),
  -- m_memberに情報をコピー
  copy_to_member AS (
    INSERT INTO m_member(
      tenant_no,
      member_no,
      member_id,
      classification,
      currency_id,
      exhibition_allow_flag,
      bid_allow_flag,
      status,
      email_delivery_flag,
      email_priority,
      member_request_no,
      create_admin_no,
      create_datetime,
      update_admin_no,
      update_datetime,
      delete_flag,
      free_field
    )
    VALUES (
      in_tenant_no,
      (SELECT member_no FROM get_member_no),
      LPAD((SELECT member_no FROM get_member_no)::text,5,'0'),
      (SELECT classification FROM create_member_request),
      'USD', -- すべてUSDで固定
      1,
      in_bid_allow_flag,
      1, --管理側で新規登録の場合は承認にする
      in_email_delivery_flag,
      (SELECT email_priority FROM create_member_request),
      (SELECT member_request_no FROM create_member_request),
      in_update_admin_no,
      now(),
      in_update_admin_no,
      now(),
      0,
      (SELECT (free_field-'password') FROM create_member_request)
    )
    RETURNING member_request_no,
              member_no,
              member_id,
              status
  ),
  -- m_userに情報をコピー
  copy_to_user AS (
    INSERT INTO m_user(
      tenant_no,
      member_no,
      user_id,
      password,
      require_password_change,
      require_confirm_token,
      token_string,
      token_expire,
      bid_allow_flag,
      free_field,
      create_admin_no,
      create_datetime,
      update_admin_no,
      update_datetime,
      delete_flag
    )
    SELECT in_tenant_no,
            M.member_no,
            CASE WHEN T.login_option = 1 THEN M.member_id
                WHEN T.login_option = 2 THEN (MR.free_field->>'email')::character varying
                ELSE M.member_id
            END,
            in_temporary_password,
            in_require_password_change, --管理側で新規登録の場合はパスワード変更を要求する
            in_require_confirm_token, --管理側で新規登録の場合は確認トークンを要求する
            in_token_string,
            in_token_expire,
            in_bid_allow_flag,
            null,
            in_update_admin_no,
            now(),
            in_update_admin_no,
            now(),
            0
    FROM copy_to_member M
    JOIN create_member_request MR ON MR.member_request_no = M.member_request_no
    JOIN m_tenant T ON T.tenant_no = in_tenant_no

    RETURNING user_no
  ),
  -- ステータス履歴に入れる
  insert_history AS (
    INSERT INTO t_member_status_history(
      tenant_no,
      member_request_no,
      user_name,
      before_status,
      after_status,
      create_admin_no,
      create_datetime,
      delete_flag
    ) VALUES (
      in_tenant_no,
      (SELECT MR.member_request_no FROM create_member_request MR),
      (SELECT MA.admin_name FROM m_admin MA WHERE MA.admin_no = in_update_admin_no),
      null, -- 管理側で新規登録の場合は変更前ステータスはない
      (SELECT status FROM copy_to_member),
      in_update_admin_no,
      now(),
      0
    )
    RETURNING member_status_history_no
  )

  SELECT copy_to_member.member_no FROM copy_to_member INTO return_member_no;

  RETURN return_member_no;

END;
$BODY$;
