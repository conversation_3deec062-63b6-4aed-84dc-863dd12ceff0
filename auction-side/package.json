{"name": "gmo-saas-auction-system-auction-side", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --mode development --port 5173", "demo": "vite --mode demo --port 5173", "build-dev": "vue-tsc --noEmit && vite build --mode development", "build-demo": "vue-tsc --noEmit && vite build --mode demo", "deploy-demo": "npm run build-demo && aws --profile saas-demo s3 sync ./dist s3://gmo-demo-saas-auction-bucket/client/auction --delete --cache-control max-age=86400,public,no-store", "build-demo2": "vue-tsc --noEmit && vite build --mode demo2", "deploy-dev": "npm run build-dev && aws --profile saas-demo s3 sync ./dist s3://gmo-demo2-saas-auction-bucket/client/auction --delete --cache-control max-age=86400,public,no-store", "deploy-demo2": "npm run build-dev && aws --profile saas-demo s3 sync ./dist s3://gmo-demo2-saas-auction-bucket/client/auction --delete --cache-control max-age=86400,public,no-store", "build-prod": "vue-tsc --noEmit && vite build --mode production", "deploy-prod": "npm run build-prod && aws --profile saas-demo s3 sync ./dist s3://gmo-demo2-saas-auction-bucket/client/auction --delete --cache-control max-age=86400,public,no-store", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "test:unit": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "npx vitest --ui", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@aws-sdk/client-s3": "^3.391.0", "@chenfengyuan/vue-countdown": "^2.1.2", "@vueuse/core": "^10.2.1", "@vueuse/integrations": "^10.2.1", "axios": "^1.4.0", "buffer": "^6.0.3", "crypto-js": "^4.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "jquery": "^3.7.0", "lodash": "^4.17.21", "pako": "^2.1.0", "photoswipe": "^5.3.8", "pinia": "^2.1.3", "pinia-plugin-persistedstate": "^3.2.1", "slick-carousel": "^1.8.1", "universal-cookie": "^4.0.4", "vue": "^3.3.4", "vue-i18n": "^9.14.1", "vue-router": "^4.2.2", "vue-slick-carousel": "^1.0.6", "vue-slick-ts": "^1.1.0", "vue-social-sharing": "^4.0.0-alpha4", "vue3-carousel": "^0.3.1", "vue3-cookies": "^1.0.6", "vue3-countdown": "^1.0.6", "vue3-slick-carousel": "^1.0.6", "websocket": "^1.0.34"}, "devDependencies": {"@eslint/js": "^9.13.0", "@mdi/font": "^7.4.47", "@rushstack/eslint-patch": "^1.10.4", "@types/jquery": "^3.5.16", "@types/node": "^24.0.10", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-vue": "^4.6.2", "@vitest/coverage-istanbul": "^3.0.4", "@vitest/coverage-v8": "^3.0.4", "@vitest/ui": "^3.0.4", "@vue/eslint-config-prettier": "^7.1.0", "@vue/test-utils": "^2.3.2", "@vue/tsconfig": "^0.7.0", "eslint": "^9.13.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.29.1", "globals": "^15.11.0", "jsdom": "^22.1.0", "prettier": "^3.3.3", "sass": "^1.81.0", "typescript": "^5.8.3", "vite": "^5.1.2", "vite-plugin-vuetify": "^2.0.4", "vitest": "^3.0.4", "vue-tsc": "^3.0.1", "vuetify": "^3.7.4"}}