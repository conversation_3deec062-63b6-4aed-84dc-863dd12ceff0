import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useReloadAttemptStore = defineStore(
  'reload-attempt',
  () => {
    const reloadAttempts = ref(0)
    const maxReloadAttempts = 3

    const increment = () => {
      reloadAttempts.value++
    }

    const reset = () => {
      reloadAttempts.value = 0
    }

    const isMaxAttemptsReached = () => reloadAttempts.value >= maxReloadAttempts

    return { reloadAttempts, maxReloadAttempts, increment, isMaxAttemptsReached, reset }
  },
  {
    persist: true
  }
)
