import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export const usePrevRouteStore = defineStore(
  'prev-route',
  () => {
    const route = useRoute()
    const router = useRouter()

    const prevRoute = ref({})

    const setPrevRoute = (r) => {
      prevRoute.value = {
        path: r?.path || '/',
        name: r?.name || 'TOP',
        fullPath: r?.fullPath || '/',
        meta: r?.meta || {}
      }
      sessionStorage.setItem('prevRoute', JSON.stringify(prevRoute.value))
    }
    const getPrevRoute = () => {
      const stored = sessionStorage.getItem('prevRoute')
      return stored ? JSON.parse(stored) : { path: '/', name: 'TOP' }
    }
    const goToPath = (path) => {
      if (path) {
        prevRoute.value = {
          path: route.path,
          name: route.meta?.name
        }
        router.push(path)
      }
    }

    return { prevRoute, setPrevRoute, goToPath, getPrevRoute }
  },
  {
    persist: true
  }
)
