import { cloneDeep } from 'lodash'
import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { REGIST_FORM } from '../defined/const'

export const useRegisterStore = defineStore('register', () => {
  const registInputs = reactive(cloneDeep(REGIST_FORM))

  const resetParams = () => {
    Object.keys(registInputs).forEach((key) => {
      registInputs[key].value = ''
    })
  }

  return { registInputs, resetParams }
})
