import { defineStore } from 'pinia'
import { reactive } from 'vue'
import { EDIT_FORM } from '../defined/const'

export const useChangeMemberStore = defineStore('change-member', () => {
  const registInputs = reactive(EDIT_FORM)

  const resetPasswords = () => {
    const keys = ['password', 'passwordConfirm', 'currentPassword']
    keys.forEach((key) => {
      const input = registInputs.find((field) => field.item === key)
      if (input) {
        input.value = ''
      }
    })
  }

  return { registInputs, resetPasswords }
})
