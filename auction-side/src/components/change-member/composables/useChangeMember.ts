import { onBeforeMount, reactive, ref, watch } from 'vue'
import { useLocale } from 'vuetify'
import { scrollToTop } from '../../../composables/common'
import useApi from '../../../composables/useApi'
import { useAuthStore } from '../../../stores/auth'
import { useChangeMemberStore } from '../../../stores/change-member'
import { useMemberStore } from '../../../stores/member-info'
import { useMessageDialogStore } from '../../../stores/message-dialog'
import { convertFullWidthToHalfWidth } from '../../../composables/common'

// Types defined in composable
export type ErrorMsg = {
  country: string | null
  ceoName: string | null
  ceoNameKana: string | null
  ceoBirthday: string | null
  companyName: string | null
  companyNameKana: string | null
  companyAddress: string | null
  establishmentDate: string | null
  companyHp: string | null
  businessContent: string | null
  invoiceNo: string | null
  tel: string | null
  antiquePermitNo: string | null
  antiquePermitDate: string | null
  antiquePermitCommission: string | null
  memberLastName: string | null
  memberName: string | null
  whatsApp: string | null
  weChat: string | null
  memo: string | null
  email: string | null
  emailConfirm: string | null
  password: string | null
  passwordConfirm: string | null
  emailDuplicated: string | null
}

export type EmailLangOption = {
  title: string
  value: string
}

export type MemberData = {
  nickname?: string
  email?: string
  country?: string
  companyName?: string
  tel?: string
  [key: string]: any
}

export type ConstantItem = {
  key_string: string
  value1: string
  value2: string
}

export type ApiResponse = {
  constants?: ConstantItem[]
  member?: MemberData
  status?: string
  message?: string
}

export type RegisterData = {
  [key: string]: any
}

export type HandleErrorMsgParams = {
  field: keyof ErrorMsg
  message: string
}

/**
 * Convert full-width characters to half-width for specific fields
 */
const fullWidthToHalfWidthConversion = (params: RegisterData): RegisterData => {
  const registerData = { ...params }
  const fieldsToConvert = ['antiquePermitNo', 'password', 'passwordConfirm', 'tel']

  Object.keys(registerData).forEach((key) => {
    if (fieldsToConvert.includes(key) && registerData[key]) {
      registerData[key] = convertFullWidthToHalfWidth(registerData[key])
    }
  })

  return registerData
}

/**
 * Email language options
 */
const emailLangList: EmailLangOption[] = [
  { title: 'EN', value: 'en' },
  { title: 'JA', value: 'ja' }
]

export function useChangeMember() {
  const { t } = useLocale()
  const dialog = useMessageDialogStore()
  const auth = useAuthStore()
  const member = useMemberStore()
  const { registInputs, resetPasswords } = useChangeMemberStore()
  const { apiExecute, parseHtmlResponseError } = useApi()

  // State
  const step = ref(1)
  const showError = ref(false)
  const constants = ref<any[]>([])

  const errorMsg = reactive<ErrorMsg>({
    country: null,
    ceoName: null,
    ceoNameKana: null,
    ceoBirthday: null,
    companyName: null,
    companyNameKana: null,
    companyAddress: null,
    establishmentDate: null,
    companyHp: null,
    businessContent: null,
    invoiceNo: null,
    tel: null,
    antiquePermitNo: null,
    antiquePermitDate: null,
    antiquePermitCommission: null,
    memberLastName: null,
    memberName: null,
    whatsApp: null,
    weChat: null,
    memo: null,
    email: null,
    emailConfirm: null,
    password: null,
    passwordConfirm: null,
    emailDuplicated: null
  })

  // Actions
  const nextStep = () => {
    step.value += 1
  }

  const prevStep = () => {
    step.value -= 1
  }

  const sendConfirmRequest = async (params: RegisterData) => {
    const registerData = fullWidthToHalfWidthConversion(params)
    const reqParams = { registerData, validateFlag: true }

    try {
      // Clear previous errors
      Object.keys(errorMsg).forEach((key) => {
        errorMsg[key as keyof ErrorMsg] = null
      })

      await apiExecute('private/change-member-info', reqParams)
      scrollToTop()

      if (!Object.values(errorMsg).some((val) => val)) {
        nextStep()
      }
    } catch (error: any) {
      const err = parseHtmlResponseError(error)
      scrollToTop()

      Object.keys(err).forEach((key) => {
        if (err[key] && key in errorMsg) {
          errorMsg[key as keyof ErrorMsg] = err[key]
        }
      })
    }
  }

  const sendRequest = async (params: RegisterData) => {
    const registerData = fullWidthToHalfWidthConversion(params)
    const reqParams = { registerData }

    try {
      await apiExecute('private/change-member-info', reqParams)
      scrollToTop()

      dialog.setShowMessage(t('register.form.changeFinish'), {
        name: 'change-member-info',
        showOkButton: true,
        showCloseButton: false
      })

      showError.value = false
    } catch (error: any) {
      console.error('Error updating member info:', error)
      scrollToTop()
      dialog.setShowMessage(t('common.error'), {
        showOkButton: true,
        showCloseButton: false
      })
    }
  }

  const getConstants = async () => {
    try {
      const res: ApiResponse = await apiExecute('private/get-change-info-constants')

      if (res?.constants) {
        constants.value = res.constants
      }

      if (res?.member) {
        ;(member as any).setMemberInfo(res)
        registInputs.forEach((input: any) => {
          input.value = res.member?.[input.item] ?? input.value
        })
        // Refresh nickname
        if (res.member.nickname) {
          auth.setNickname(res.member.nickname)
        }
      }
    } catch (error: any) {
      console.error('Error getting constants:', error)
    }
  }

  const showWithdrawalConfirmDialog = () => {
    dialog.setShowMessage(t('register.form.withdrawConfirm'), {
      name: 'withdraw-member-open',
      showConfirmButton: true
    })
  }

  const handleErrorMsg = ({ field, message }: HandleErrorMsgParams) => {
    errorMsg[field] = message
  }

  // Watchers
  watch(
    () => dialog.showMessageDialog,
    (newVal) => {
      switch (dialog.dialogName) {
        case 'change-member-info':
          if (!newVal && !showError.value) {
            prevStep()
          }
          break
        case 'withdraw-member-finish':
          if (!newVal && !showError.value) {
            auth.handleLogout()
          }
          break
        default:
          break
      }
    }
  )

  watch(
    () => dialog.clickedConfirm,
    async (newVal) => {
      if (newVal) {
        switch (dialog.dialogName) {
          case 'withdraw-member-open':
            dialog.handleClose()
            try {
              await apiExecute('private/withdraw-member')
              dialog.setShowMessage(t('register.form.withdrawFinish'), {
                name: 'withdraw-member-finish',
                showOkButton: true,
                showCloseButton: false
              })
              showError.value = false
            } catch (error: any) {
              const err = parseHtmlResponseError(error)
              dialog.setShowMessage(err.message, { isErr: true })
            }
            break
          default:
            break
        }
      }
    }
  )

  // Lifecycle
  onBeforeMount(async () => {
    try {
      await getConstants()
      resetPasswords()
    } catch (error: any) {
      console.error('Error in ChangeMember onBeforeMount:', error)
    }
  })

  return {
    // State
    step,
    constants,
    errorMsg,
    emailLangList,

    // Actions
    nextStep,
    prevStep,
    sendConfirmRequest,
    sendRequest,
    showWithdrawalConfirmDialog,
    handleErrorMsg
  }
}
