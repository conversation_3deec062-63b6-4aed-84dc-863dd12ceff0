import { onMounted, onUnmounted, nextTick } from 'vue'
import $ from 'jquery'

export function useSlickSlider() {
  let resizeTimer = null

  const initSliderIfEnoughItems = (selector = '.list-item-gallery') => {
    const $slider = $(selector)

    if (!$slider.length) {
      console.log('Slider element not found:', selector)
      return
    }

    if (typeof $slider.slick !== 'function') {
      console.error('Slick carousel is not loaded')
      return
    }

    const itemCount = $slider.find('li').length
    let slidesToShow = 5
    const windowWidth = window.innerWidth

    if (windowWidth <= 767) {
      slidesToShow = 2
    } else if (windowWidth <= 1080) {
      slidesToShow = 3
    }

    try {
      if ($slider.hasClass('slick-initialized')) {
        $slider.slick('unslick')
      }

      if (itemCount > slidesToShow) {
        $slider.removeClass('not-slick').slick({
          arrows: true,
          dots: true,
          infinite: true,
          slidesToShow: slidesToShow,
          slidesToScroll: 1,
          responsive: [
            { breakpoint: 1080, settings: { slidesToShow: 3 } },
            { breakpoint: 767, settings: { slidesToShow: 2 } }
          ]
        })
        console.log('Slick slider initialized successfully')
      } else {
        $slider.addClass('not-slick')
        console.log('Not enough items for slider, showing static layout')
      }
    } catch (error) {
      console.error('Error initializing slick slider:', error)
    }
  }

  const handleResize = () => {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(() => {
      initSliderIfEnoughItems()
    }, 300)
  }

  const initSlider = async (selector) => {
    await nextTick()

    setTimeout(() => {
      initSliderIfEnoughItems(selector)
    }, 100)

    window.addEventListener('resize', handleResize)
  }

  const cleanupSlider = (selector = '.list-item-gallery') => {
    window.removeEventListener('resize', handleResize)
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }

    try {
      const $slider = $(selector)
      if (
        $slider.length &&
        typeof $slider.slick === 'function' &&
        $slider.hasClass('slick-initialized')
      ) {
        $slider.slick('unslick')
      }
    } catch (error) {
      console.error('Error cleaning up slick slider:', error)
    }
  }

  return {
    initSlider,
    cleanupSlider,
    initSliderIfEnoughItems,
    handleResize
  }
}
