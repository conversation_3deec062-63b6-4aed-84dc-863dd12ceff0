<script setup>
import { defineProps } from 'vue'
import { useLocale } from 'vuetify'
defineProps({
  freeField: {
    type: Object,
    default: null
  }
})
const { t } = useLocale()
</script>
<template>
  <section id="target-item">
    <ul>
      <li>
        <div class="target-item-txt">
          <div class="target-item-data">
            <dl>
              <dt>{{ t('productDetail.info.maker') }}</dt>
              <dd>{{ freeField?.maker }}</dd>
            </dl>
            <dl>
              <dt>{{ t('productDetail.info.productName') }}</dt>
              <dd>{{ freeField?.product_name }}</dd>
            </dl>
            <dl>
              <dt>{{ t('productDetail.info.sim') }}</dt>
              <dd>{{ freeField?.sim }}</dd>
            </dl>
            <dl>
              <dt>{{ t('productDetail.info.capacity') }}</dt>
              <dd>{{ freeField?.capacity }}</dd>
            </dl>
            <dl>
              <dt>{{ t('productDetail.info.color') }}</dt>
              <dd>{{ freeField?.color }}</dd>
            </dl>
            <dl>
              <dt>{{ t('productDetail.info.rank') }}</dt>
              <dd>{{ freeField?.rank }}</dd>
            </dl>
            <dl>
              <dt>{{ t('productDetail.info.note1') }}</dt>
              <dd>{{ freeField?.note1 }}</dd>
            </dl>
            <dl>
              <dt>{{ t('productDetail.info.note2') }}</dt>
              <dd>{{ freeField?.note2 }}</dd>
            </dl>
          </div>
        </div>
      </li>
    </ul>
  </section>
</template>
