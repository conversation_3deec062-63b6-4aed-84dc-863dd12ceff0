<script setup>
import { PATH_NAME } from '@/defined/const'
import { useLocale } from 'vuetify'

const { t: translate } = useLocale()
</script>
<template>
  <main id="main" class="reminder">
    <div id="pNav">
      <ul>
        <li><a href="./">TOP</a></li>
        <li>{{ translate('login.reminderForgetPassword') }}</li>
      </ul>
    </div>
    <section id="login">
      <h1 class="mb0">{{ translate('login.reminderForgetPassword') }}</h1>
      <div class="container">
        <div class="remind-msg-comp">
          <p>{{ translate('login.reminderCompleteMessage') }}</p>
          <div class="remind-comp-btn">
            <router-link :to="PATH_NAME.LOGIN" class="btnBsc-Black">
              {{ translate('login.reminderCompleteButton') }}
            </router-link>
          </div>
        </div>
      </div>
    </section>
  </main>
</template>
<style scoped src="@/assets/css/home.css" />
