import { onMounted, onUnmounted } from 'vue'

/**
 * フッター手前でストップ：スクロール時に「トップへ戻る」ボタンを動的に位置調整する処理を設定
 */
const setupScrollLogic = () => {
  const handleScroll = () => {
    const pageTop = document.getElementById('page_top')
    const footer = document.querySelector('footer')

    if (!pageTop || !footer) return

    const scrollHeight = document.documentElement.scrollHeight
    const scrollPosition = window.innerHeight + window.scrollY

    const footerHeight = footer.offsetHeight - 30
    const footerOffsetTop = footer.offsetTop

    if (window.innerWidth <= 768) {
      if (scrollPosition >= footerOffsetTop) {
        // scrolled to or past the top of the 「footer」
        pageTop.style.position = 'absolute'
        pageTop.style.top = '-20px'
      } else {
        pageTop.style.position = 'fixed'
        pageTop.style.bottom = '13px'
        pageTop.style.top = 'auto'
      }
    } else if (scrollHeight - scrollPosition <= footerHeight) {
      // scrolled near the bottom of the page, within the footer's height.
      pageTop.style.position = 'absolute'
      pageTop.style.bottom = `${footerHeight}px`
    } else {
      pageTop.style.position = 'fixed'
      pageTop.style.bottom = '17px'
      pageTop.style.top = 'auto'
    }
  }

  onMounted(() => {
    window.addEventListener('scroll', handleScroll)
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
  })
}

export default setupScrollLogic
