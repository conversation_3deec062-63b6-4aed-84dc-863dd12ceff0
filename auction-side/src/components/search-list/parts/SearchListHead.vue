<script setup>
import { useLocale } from 'vuetify'
import { PATH_NAME } from '../../../defined/const'

defineProps(['searchKey', 'count', 'category'])
const { t: translate } = useLocale()
</script>
<template>
  <div id="pNav" class="search-list-head">
    <ul>
      <li><router-link :to="PATH_NAME.TOP">TOP</router-link></li>
      <li>{{ translate($route.meta.name) }}</li>
    </ul>
    <!-- <h2 class="page-ttl">
      <div class="ttl">
        <span class="red">R</span>esult
        <p class="sub">検索語：{{ searchKey }}</p>
        <p class="sub">全{{ count }}件</p>
      </div>
    </h2> -->
  </div>
</template>
