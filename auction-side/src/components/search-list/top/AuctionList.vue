<script setup>
import { computed, defineAsyncComponent, defineProps } from 'vue'
import { useLocale } from 'vuetify'
import AuctionContent from '../parts/AuctionContent.vue'

const AuctionInfo = defineAsyncComponent(() => import('../parts/AuctionInfo.vue'))
const AuctionItem = defineAsyncComponent(() => import('./AuctionItem.vue'))

const props = defineProps(['exhibitionInfo', 'productList'])
const isAscendingAuction = computed(
  () => Number(props.exhibitionInfo?.auction_classification) === 1
)

const { t } = useLocale()
</script>
<template>
  <div id="top-auction-list-item">
    <AuctionInfo :exhibitionInfo="exhibitionInfo" />
    <AuctionContent
      :productList="productList"
      :exhibitionInfo="exhibitionInfo"
      :isAscendingAuction="isAscendingAuction"
    >
      <template v-slot:header>
        <tr>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.maker') }}</th>
          <th rowspan="1" colspan="1" class="text-break">
            {{ t('productDetail.info.productName') }}
          </th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.sim') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.capacity') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.color') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.rank') }}</th>
          <th rowspan="1" colspan="1">
            {{
              isAscendingAuction
                ? t('productDetail.info.startPrice')
                : t('productDetail.info.quantity')
            }}
          </th>

          <th v-if="isAscendingAuction" rowspan="1" colspan="1" class="ensure-w text-break">
            {{ t('productDetail.info.currentPrice') }}
          </th>
          <th
            v-else
            rowspan="1"
            colspan="1"
            class="ensure-w text-break"
            v-html="t('productDetail.info.lowestBidQuantity')"
          ></th>

          <th v-if="isAscendingAuction" rowspan="1" colspan="1" class="ensure-w text-break">
            {{ t('COMMON_BID_LABEL') }}
          </th>
          <th
            v-else
            rowspan="1"
            colspan="1"
            class="ensure-w text-break"
            v-html="t('productDetail.info.lowestBidPrice')"
          ></th>

          <th rowspan="1" colspan="1">{{ t('productDetail.info.favorite') }}</th>
        </tr>
      </template>
      <template v-slot:item="{ item, favorite }">
        <AuctionItem :item="item" :favorite="favorite" :isAscendingAuction="isAscendingAuction" />
      </template>
    </AuctionContent>
  </div>
</template>
