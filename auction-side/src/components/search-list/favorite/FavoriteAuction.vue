<script setup>
import { computed, defineAsyncComponent, defineEmits, defineProps } from 'vue'
import { useLocale } from 'vuetify'
import AuctionContent from '../parts/AuctionContent.vue'

const AuctionInfo = defineAsyncComponent(() => import('../parts/AuctionInfo.vue'))
const AuctionItem = defineAsyncComponent(() => import('./AuctionItem.vue'))

const emit = defineEmits(['refresh'])
const props = defineProps(['exhibitionInfo', 'productList'])
const isAscendingAuction = computed(
  () => Number(props.exhibitionInfo?.auction_classification) === 1
)

const { t } = useLocale()

const refresh = () => {
  emit('refresh')
}
</script>
<template>
  <div class="auction-conteiner">
    <AuctionInfo :exhibitionInfo="exhibitionInfo" />
    <AuctionContent
      :productList="productList"
      :exhibitionInfo="exhibitionInfo"
      :isAscendingAuction="isAscendingAuction"
      @refresh="refresh"
    >
      <template v-slot:header>
        <tr v-if="!isAscendingAuction">
          <th rowspan="1" colspan="1" class="f-sm">
            {{ t('favorite.deleteFavorite1') }}<br />{{ t('favorite.deleteFavorite2') }}
          </th>
          <th rowspan="1" colspan="1" class="text-break">
            {{ t('productDetail.info.productName') }}
          </th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.sim') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.capacity') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.color') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.rank') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.quantity') }}</th>
          <th
            rowspan="1"
            colspan="1"
            class="text-break"
            v-html="t('productDetail.info.lowestBidQuantity')"
          ></th>
          <th
            rowspan="1"
            colspan="1"
            class="text-break"
            v-html="t('productDetail.info.lowestBidPrice')"
          ></th>
          <th rowspan="1" colspan="1">{{ t('favorite.bidQuantity') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.bidUnitPrice') }}</th>
          <th
            rowspan="1"
            colspan="1"
            class="text-break"
            v-html="t('favorite.subTotalBidPriceHeader')"
          ></th>
          <th rowspan="1" colspan="1">{{ t('COMMON_BID_LABEL') }}</th>
        </tr>
        <tr v-else>
          <th rowspan="1" colspan="1" class="f-sm">
            {{ t('favorite.deleteFavorite1') }}<br />{{ t('favorite.deleteFavorite2') }}
          </th>
          <th rowspan="1" colspan="1" class="col-large text-break">
            {{ t('productDetail.info.productName') }}
          </th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.sim') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.capacity') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.color') }}</th>
          <th rowspan="1" colspan="1">{{ t('productDetail.info.rank') }}</th>
          <!-- 現在価格 -->
          <th rowspan="1" colspan="1">{{ t('productDetail.info.currentPrice') }}</th>
          <!-- ステータス -->
          <th rowspan="1" colspan="1">{{ t('BID_STATUS') }}</th>
          <!-- 入札価格 -->
          <th rowspan="1" colspan="1">{{ t('productDetail.bidPriceForAscAuction') }}</th>
          <!-- 入札  -->
          <th rowspan="1" colspan="1">{{ t('COMMON_BID_LABEL') }}</th>
        </tr>
      </template>
      <template v-slot:item="{ item, favorite }">
        <AuctionItem
          :item="item"
          :exhibitionInfo="exhibitionInfo"
          :favorite="favorite"
          @refresh="refresh"
        />
      </template>
    </AuctionContent>
  </div>
</template>

<style lang="css" scoped>
.col-large {
  width: 25%;
  min-width: 300px;
  max-width: 400px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; /* Adds '...' for overflowed text */
}
</style>
