<script setup>
import { useLocale } from 'vuetify'

defineProps(['freeField'])

const { t } = useLocale()
</script>
<template>
  <div class="contents-wrap">
    <table>
      <tbody>
        <tr class="spec">
          <th>{{ t('productDetail.info.maker') }}</th>
          <td>{{ freeField?.maker }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.productName') }}</th>
          <td>{{ freeField?.product_name }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.sim') }}</th>
          <td>{{ freeField?.sim }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.capacity') }}</th>
          <td>{{ freeField?.capacity }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.color') }}</th>
          <td>{{ freeField?.color }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.rank') }}</th>
          <td>{{ freeField?.rank }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.note1') }}</th>
          <td>{{ freeField?.note1 }}</td>
        </tr>
        <tr class="spec">
          <th>{{ t('productDetail.info.note2') }}</th>
          <td>{{ freeField?.note2 }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
