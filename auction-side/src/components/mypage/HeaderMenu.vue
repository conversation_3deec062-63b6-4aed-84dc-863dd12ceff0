<script setup>
import { useRoute } from 'vue-router'
import { useLocale } from 'vuetify'
import { PATH_NAME } from '../../defined/const'

const route = useRoute()
const { t: translate } = useLocale()
</script>
<template>
  <section id="mypage-head">
    <div class="container">
      <div class="nav-wrap">
        <div class="nav-content" :class="route.path === PATH_NAME.FAVORITES ? 'active' : ''">
          <RouterLink :to="PATH_NAME.FAVORITES"
            ><div class="label favorite">{{ translate('favorite.title') }}</div></RouterLink
          >
        </div>
        <div class="nav-content" :class="route.path === PATH_NAME.BIDS ? 'active' : ''">
          <RouterLink :to="PATH_NAME.BIDS"
            ><div class="label bid">{{ translate('auction.bidOngoing') }}</div></RouterLink
          >
        </div>
        <div class="nav-content" :class="route.path === PATH_NAME.BID_HISTORY ? 'active' : ''">
          <RouterLink :to="PATH_NAME.BID_HISTORY"
            ><div class="label sbid">{{ translate('auction.bidHistory') }}</div></RouterLink
          >
        </div>
        <div class="nav-content" :class="route.path === PATH_NAME.MYPAGE ? 'active' : ''">
          <RouterLink :to="PATH_NAME.MYPAGE"
            ><div class="label account">{{ translate('user.editProfile') }}</div></RouterLink
          >
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped>
/* Small devices (landscape phones, less than 768px) */
@media screen and (max-width: 767.98px) {
  #main #mypage-head .nav-wrap .nav-content a .label.favorite:before {
    width: 22px;
  }
  #main #mypage-head .nav-wrap .nav-content a .label.bid:before {
    width: 22px;
  }
  #main #mypage-head .nav-wrap .nav-content a .label.sbid:before {
    width: 22px;
  }
  #main #mypage-head .nav-wrap .nav-content a .label.account:before {
    width: 22px;
  }
}
</style>
