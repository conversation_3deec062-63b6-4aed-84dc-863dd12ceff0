<script setup lang="ts">
import { defineAsyncComponent } from 'vue'
import { useRegister } from './composables/useRegister.ts'

const EntryForm = defineAsyncComponent(
  () => import(/* webpackChunkName: "EntryForm" */ './components/EntryForm.vue')
)
const ConfirmData = defineAsyncComponent(
  () => import(/* webpackChunkName: "ConfirmData" */ './components/ConfirmData.vue')
)

const {
  step,
  constants,
  errorMsg,
  emailLangList,
  nextStep,
  prevStep,
  sendConfirmRequest,
  sendRequest,
  handleErrorMsg,
  t
} = useRegister()
</script>
<template>
  <div id="pNav">
    <ul>
      <li><a href="../">TOP</a></li>
      <li>{{ t('register.title') }}</li>
    </ul>
  </div>
  <section id="entry">
    <h1 class="mb0">{{ t('register.title') }}</h1>
    <div class="container">
      <EntryForm
        v-if="step === 1"
        :constants="constants"
        :errorMsg="errorMsg"
        @confirm-inputs="sendConfirmRequest"
        @update:errorMsg="handleErrorMsg"
        :emailLangOptions="emailLangList"
      />
      <ConfirmData
        v-else-if="step === 2"
        :constants="constants"
        :errorMsg="errorMsg"
        @back="prevStep"
        @regist-member="sendRequest"
        :emailLangOptions="emailLangList"
      />
    </div>
  </section>
</template>
