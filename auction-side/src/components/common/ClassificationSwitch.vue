<script setup>
import { computed } from 'vue'
import { useLocale } from 'vuetify'
import { CLASSIFICATIONS } from '../../defined/const'
import { useSearchResultStore } from '../../stores/search-results'
import { isMobile } from '../../utils'

const searchResultStore = useSearchResultStore()

const { t } = useLocale()

const isAscendingActive = computed(
  () => searchResultStore.myPageSelectedClassification === CLASSIFICATIONS.ASCENDING
)

const emit = defineEmits(['click:changeClassification'])
</script>

<template>
  <section id="method-switch">
    <div class="container">
      <div class="nav-wrap">
        <a
          @click="emit('click:changeClassification', CLASSIFICATIONS.SEALED)"
          :class="{ 'nav-content-active': !isAscendingActive }"
        >
          <span v-if="!isMobile">{{ t('CLASSIFICATION_SEALED') }}</span>
          <span v-else>{{ t('SEALED') }}</span>
        </a>
        <a
          @click="emit('click:changeClassification', CLASSIFICATIONS.ASCENDING)"
          :class="{ 'nav-content-active': isAscendingActive }"
        >
          <span v-if="!isMobile">{{ t('CLASSIFICATION_ASCENDING') }}</span>
          <span v-else>{{ t('ASCENDING') }}</span>
        </a>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped>
.nav-wrap a {
  cursor: pointer;
}
.nav-content-active {
  cursor: none;
  background-color: #007bff;
  color: white;
  font-weight: bold;
  pointer-events: none;
}
</style>
