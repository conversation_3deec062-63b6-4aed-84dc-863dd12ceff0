<script setup>
import { ref, toRefs } from 'vue'
import { useLocale } from 'vuetify'

const props = defineProps({
  input: {
    type: Object,
    required: true
  },
  autocomplete: {
    type: String,
    required: false
  }
})

const { input } = toRefs(props)
const emit = defineEmits(['error'])
const { t } = useLocale()
const selectedCountry = ref('JP')

const isKatakana = (str) => {
  const regex = /^[\u30A0-\u30FF]+$/
  return regex.test(str)
}

const validateKatakana = (value) => {
  const errorMsg = t('register.form.kataKanaError')
  if (input.value.katakanaCheck && !isKatakana(value)) {
    emit('error', { field: input.value.item, message: errorMsg })
  } else {
    emit('error', { field: input.value.item, message: null })
  }
}
</script>

<template>
  <input
    type="text"
    :class="input.class"
    :required="input.required(selectedCountry)"
    :placeholder="input.placeholder"
    :autocomplete="autocomplete"
    v-model="input.value"
    :disabled="input.disabled"
    :maxlength="input.length"
    @blur="input.katakanaCheck && validateKatakana(input.value)"
  />
  <span v-if="input.append" class="ipt-rule">{{ input.append(t) }}</span>
</template>

<style scoped>
#main #entry-form table.tbl-entry input.iptW-M,
#main #entry-form table.tbl-entry input.iptW-MM {
  background-color: #fff;
}

#main #entry-form table.tbl-entry input.iptW-M:disabled,
#main #entry-form table.tbl-entry input.iptW-MM:disabled {
  background-color: #d6d6d6;
}
</style>
