<script setup>
import { computed, defineAsyncComponent, onMounted, watch } from 'vue'
import { onBeforeRouteLeave, useRoute } from 'vue-router'

import { useLocale } from 'vuetify'
import SearchListHead from '../../components/search-list/parts/SearchListHead.vue'
import useSearchProducts from '../../composables/searchProducts'
import { useSearchResultStore } from '../../stores/search-results'

const ProductList = defineAsyncComponent(
  () => import(/* WebpackChunkName: "ProductList" */ '../../components/search-list/ProductList.vue')
)
const FilterBox = defineAsyncComponent(
  () =>
    import(/* webpackChunkName: "FilterBox" */ '../../components/search-list/parts/FilterBox.vue')
)

const route = useRoute()
const searchResultStore = useSearchResultStore()
const { searchAllSuccessfulBidHistory, resetParams, getConstants } = useSearchProducts()

const { t: translate, current } = useLocale()

const productList = computed(() => searchResultStore.productList.all)
const exhibitionList = computed(() => searchResultStore.productList.exhibitionList)

const handleSearch = () => {
  searchAllSuccessfulBidHistory()
}

const getData = async () => {
  await Promise.all([getConstants(), handleSearch()])
}

// 画面表示時に検索処理を実行
onMounted(async () => {
  resetParams()
  await getData()
})

watch(
  () => current.value,
  async () => {
    await getData()
  }
)

onBeforeRouteLeave(() => {
  searchResultStore.viewMore = 1
})
</script>
<template>
  <div>
    <SearchListHead
      :search-key="searchResultStore.searchKeyTopAfter"
      :count="searchResultStore.totalCount"
      :category="searchResultStore.searchCategory"
    />
    <h1>{{ translate(route.meta.name) }}</h1>
    <FilterBox @clicked:search="handleSearch" />
    <section id="list-auction">
      <div class="container">
        <template v-for="exh in exhibitionList" :key="exh.exhibition_no">
          <ProductList
            v-if="productList.some((x) => x.exhibition_no === exh.exhibition_no)"
            :productList="productList.filter((x) => x.exhibition_no === exh.exhibition_no)"
            :exhibitionInfo="exh"
          />
        </template>
      </div>
    </section>
  </div>
</template>
