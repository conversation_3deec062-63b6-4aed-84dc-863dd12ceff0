<script setup>
import { computed, defineAsyncComponent, onMounted, watch } from 'vue'
import { onBeforeRouteLeave, useRoute } from 'vue-router'
import { CLASSIFICATIONS, PATH_NAME } from '../../defined/const'

import { useLocale } from 'vuetify'
import ClassificationSwitch from '../../components/common/ClassificationSwitch.vue'
import FixBottomBulkBid from '../../components/common/FixBottomBulkBid.vue'
import BidConfirmModal from '../../components/detail/BidConfirmModal.vue'
import HeaderMenu from '../../components/mypage/HeaderMenu.vue'
import useSearchProducts from '../../composables/searchProducts'
import useWebSocket from '../../composables/websocket'
import { useAuthStore } from '../../stores/auth'
import { useBidConfirmStore } from '../../stores/bidConfirm'
import { useSearchResultStore } from '../../stores/search-results'

const ProductList = defineAsyncComponent(
  () => import(/* WebpackChunkName: "ProductList" */ '../../components/search-list/ProductList.vue')
)
const FilterBox = defineAsyncComponent(
  () =>
    import(/* webpackChunkName: "FilterBox" */ '../../components/search-list/parts/FilterBox.vue')
)

const { t, current } = useLocale()
const route = useRoute()
const searchResultStore = useSearchResultStore()
const bid = useBidConfirmStore()
const { search: searchOnLoad, resetParams, getConstants } = useSearchProducts()
const auth = useAuthStore()
const { connectWs } = useWebSocket(searchResultStore, route)

const productList = computed(() => searchResultStore.productList.all)
const exhibitionList = computed(() => searchResultStore.productList.exhibitionList)
const isAscendingAuction = computed(
  () => searchResultStore.myPageSelectedClassification === CLASSIFICATIONS.ASCENDING
)

const getData = async (classification = CLASSIFICATIONS.SEALED) => {
  searchResultStore.myPageSelectedClassification = classification
  const path = route.path
  if (path === PATH_NAME.FAVORITES) {
    await searchOnLoad({ favorite: true, auction_classification: classification })
  } else if (path === PATH_NAME.BIDS) {
    await searchOnLoad({ bidding: true, unSoldOut: true, auction_classification: classification })
  }
}

const getInitData = async () => {
  await Promise.all([getConstants(), getData()])
}

// 各画面取得
const handleSearch = async () => {
  resetParams()
  await getData()
}

const refreshList = async (classification) => {
  const currentClassification =
    classification || searchResultStore.myPageSelectedClassification || CLASSIFICATIONS.SEALED
  await getData(currentClassification)
}

// パスが異なる場合のみ、再レンダリングする。
watch(
  () => route.path,
  (newPath, oldPath) => {
    if (newPath !== oldPath) {
      handleSearch()
    }
  }
)

watch(
  () => current?.value,
  async () => {
    await getInitData()
  }
)

// 画面表示時に検索処理を実行
onMounted(async () => {
  // ログインしている場合、WebSocket接続
  if (auth.token) {
    connectWs(auth.token)
  }

  resetParams()
  await getInitData()
})

onBeforeRouteLeave(() => {
  searchResultStore.viewMore = 1
})
</script>
<template>
  <div>
    <!-- My page favorite -->
    <div class="ttl-mypage">
      <p class="ttl">{{ t('user.myPage') }}</p>
    </div>

    <HeaderMenu />
    <ClassificationSwitch @click:changeClassification="getData" />

    <h1 class="mb0">
      {{ t(route.meta.label) }}
      <span
        ><span class="type sealed">{{
          searchResultStore.myPageSelectedClassification === CLASSIFICATIONS.ASCENDING
            ? t('CLASSIFICATION_ASCENDING')
            : t('CLASSIFICATION_SEALED')
        }}</span></span
      >
    </h1>
    <FilterBox
      @clicked:search="handleSearch"
      :classification="searchResultStore.myPageSelectedClassification"
    />
    <section id="list-auction">
      <div class="container list-auction-favorite">
        <template v-for="exh in exhibitionList" :key="exh.exhibition_no">
          <ProductList
            v-if="productList.some((x) => x.exhibition_no === exh.exhibition_no)"
            :productList="productList.filter((x) => x.exhibition_no === exh.exhibition_no)"
            :exhibitionInfo="exh"
            @refresh="refreshList"
          />
        </template>
        <FixBottomBulkBid v-if="!isAscendingAuction" />
      </div>
    </section>
    <!--ModalBid Start-->
    <BidConfirmModal
      v-model="bid.showBidConfirm"
      @refresh="refreshList"
      :isAscendingAuction="isAscendingAuction"
    />
    <!--ModalBid End-->
  </div>
</template>
