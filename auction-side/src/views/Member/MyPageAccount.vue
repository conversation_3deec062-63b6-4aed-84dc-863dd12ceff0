<script setup>
import { defineAsyncComponent } from 'vue'
import { useRoute } from 'vue-router'
import { useLocale } from 'vuetify'
import { PATH_NAME } from '../../defined/const'

const ChangeMember = defineAsyncComponent(
  () => import('../../components/change-member/ChangeMember.vue')
)

const { t } = useLocale()
const route = useRoute()
</script>
<template>
  <div>
    <div class="ttl-mypage">
      <p class="ttl">{{ t('user.myPage') }}</p>
    </div>
    <section id="mypage-head">
      <div class="container">
        <div class="nav-wrap">
          <div class="nav-content" :class="route.path === PATH_NAME.FAVORITES ? 'active' : ''">
            <RouterLink :to="PATH_NAME.FAVORITES"
              ><div class="label favorite">{{ t('favorite.title') }}</div></RouterLink
            >
          </div>
          <div class="nav-content" :class="route.path === PATH_NAME.BIDS ? 'active' : ''">
            <RouterLink :to="PATH_NAME.BIDS"
              ><div class="label bid">{{ t('auction.bidOngoing') }}</div></RouterLink
            >
          </div>
          <div class="nav-content" :class="route.path === PATH_NAME.BID_HISTORY ? 'active' : ''">
            <RouterLink :to="PATH_NAME.BID_HISTORY"
              ><div class="label sbid">{{ t('auction.bidHistory') }}</div></RouterLink
            >
          </div>
          <div class="nav-content" :class="route.path === PATH_NAME.MYPAGE ? 'active' : ''">
            <RouterLink :to="PATH_NAME.MYPAGE"
              ><div class="label account">{{ t('user.editProfile') }}</div></RouterLink
            >
          </div>
        </div>
      </div>
    </section>

    <h1>{{ t('user.editProfile') }}</h1>

    <section>
      <div class="container">
        <Suspense>
          <template #default>
            <ChangeMember />
          </template>
          <template #fallback>
            <div class="loading-container">
              <p>読み込み中...</p>
            </div>
          </template>
        </Suspense>
      </div>
    </section>
  </div>
</template>
