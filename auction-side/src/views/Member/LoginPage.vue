<script setup lang="ts">
import { defineAsyncComponent } from 'vue'

const Login = defineAsyncComponent(
  () => import(/* webpackChunkName: "Login" */ '../../components/login/LoginComponent.vue')
)
const PageTopLink = defineAsyncComponent(
  () => import(/* webpackChunkName: "PageTopLink" */ '../../components/parts/PageTopLink.vue')
)
</script>
<template>
  <div>
    <PageTopLink />
    <Login />
  </div>
</template>
