<script setup>
import { defineAsyncComponent } from 'vue'
import { useReminder } from '../../stores/reminder'
const Reminder = defineAsyncComponent(() => import('../../components/reminder/Reminder.vue'))
const ReminderComplete = defineAsyncComponent(
  () => import('../../components/reminder/ReminderComplete.vue')
)

const reminderStore = useReminder()
reminderStore.completedFlag = false
</script>
<template>
  <ReminderComplete v-if="reminderStore.completedFlag" />
  <Reminder v-else />
</template>
