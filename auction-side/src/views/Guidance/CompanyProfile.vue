<script setup>
import { ref, watch } from 'vue'
import { useLocale } from 'vuetify'
import CompanyProfileEN from './Locale/CompanyProfileEN.vue'
import CompanyProfileJA from './Locale/CompanyProfileJA.vue'

const { current } = useLocale()
const currentLocale = ref(current.value)

watch(
  () => current.value,
  () => {
    currentLocale.value = current.value
  }
)
</script>

<template>
  <CompanyProfileJA v-if="currentLocale === 'ja'" />
  <CompanyProfileEN v-else />
</template>
