@charset "UTF-8";
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;700&display=swap");
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *RESET
 * *********************************************************************** */
html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, abbr, address, cite, code, del, dfn, em, img, ins, kbd, q, samp, small, strong, var, b, i, a, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary, time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
  font-style: normal;
  font-weight: normal;
  text-align: left;
  text-indent: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after {
  content: "";
  content: none;
}

q:before, q:after {
  content: "";
  content: none;
}

input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
  font-size: inherit;
}

th, td {
  text-align: left;
  vertical-align: top;
}

/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *SET
 * *********************************************************************** */
html {
  height: 100%;
}

body {
  height: 100%;
  font-family: "Noto Sans JP", sans-serif;
  -webkit-font-smoothing: antialiased;
  font-weight: 400;
  letter-spacing: 1px;
  -webkit-font-feature-settings: "palt";
          font-feature-settings: "palt";
  font-size: 1rem;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -webkit-animation: fadeIn 2s ease 0s 1 normal;
          animation: fadeIn 2s ease 0s 1 normal;
  word-break: break-all;
  background: #fff;
  color: #000;
}
@media screen and (max-width: 767px) {
  body {
    font-size: 0.9rem;
  }
}

img {
  vertical-align: top;
  max-width: 100%;
  height: auto;
  width/***/: auto;
}

a {
  text-decoration: none;
  color: #000;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
a:active, a:hover, a:focus {
  outline: none;
}
a:hover {
  filter: alpha(opacity=80);
  -ms-filter: "alpha(opacity=80)";
  opacity: 0.8;
  zoom: 1;
}
a img {
  opacity: 1;
  filter: alpha(opacity=100);
}

input[type=image], input[type=submit] {
  opacity: 1;
  filter: alpha(opacity=100);
}

a:hover img.noTrans {
  opacity: 1;
  filter: alpha(opacity=100);
  -ms-filter: "alpha(opacity=100)";
}
a[href^="tel:"] {
  pointer-events: none;
  cursor: text;
}
@media screen and (max-width: 767px) {
  a[href^="tel:"] {
    pointer-events: auto;
  }
}
a[href^="tel:"]:hover {
  opacity: 1;
}

input, textarea, select, button {
  border: 0 none;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  outline: 0;
  border-radius: 0;
  font-family: inherit;
  color: inherit;
}

input[type=text], input[type=tel], input[type=email], input[type=number], input[type=url], input[type=password] {
  height: 54px;
  padding: 11px 20px;
  color: inherit;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 54px;
  -webkit-appearance: none;
}
@media screen and (max-width: 767px) {
  input[type=text], input[type=tel], input[type=email], input[type=number], input[type=url], input[type=password] {
    height: 14vw;
    font-size: 3.8vw;
  }
}
input::-webkit-input-placeholder {
  color: #ccc;
  font-size: 0.9rem;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
input::-moz-placeholder {
  color: #ccc;
  font-size: 0.9rem;
  transform: translateY(-1px);
}
input:-ms-input-placeholder {
  color: #ccc;
  font-size: 0.9rem;
  transform: translateY(-1px);
}
input::-ms-input-placeholder {
  color: #ccc;
  font-size: 0.9rem;
  transform: translateY(-1px);
}
input::placeholder {
  color: #ccc;
  font-size: 0.9rem;
  -webkit-transform: translateY(-1px);
          transform: translateY(-1px);
}
@media screen and (max-width: 767px) {
  input::-webkit-input-placeholder {
    font-size: 3.8vw;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  input::-moz-placeholder {
    font-size: 3.8vw;
    transform: translateY(0);
  }
  input:-ms-input-placeholder {
    font-size: 3.8vw;
    transform: translateY(0);
  }
  input::-ms-input-placeholder {
    font-size: 3.8vw;
    transform: translateY(0);
  }
  input::placeholder {
    font-size: 3.8vw;
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}

textarea {
  border: 1px solid #e4e4e4;
  color: inherit;
  font-size: 16px;
  padding: 11px 20px;
  -webkit-appearance: none;
}

input[type=text].err, input[type=tel].err, input[type=email].err, input[type=number].err, input[type=url].err, input[type=password].err {
  background-color: #fffafa !important;
  border: solid 1px #f00 !important;
}

textarea.err {
  background-color: #fffafa !important;
  border: solid 1px #f00 !important;
}

select {
  border: 1px solid #e4e4e4;
  background-color: #fff;
  color: inherit;
  font-size: 16px;
}
select:hover {
  cursor: pointer;
}

.select-style {
  position: relative;
}
.select-style select {
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  font-size: 18px;
  padding: 10px 20px 11px;
  width: 100%;
}
.select-style select::-ms-expand {
  display: none;
}
.select-style::after {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-top: 8px solid #7D7D7D;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  pointer-events: none;
}

label {
  cursor: pointer;
}

input[type=checkbox].checkbox-input, input[type=radio].checkbox-input {
  display: none;
}

.checkbox-parts {
  display: inline-block;
  padding-left: 36px;
  position: relative;
  font-weight: 500;
  font-size: 16px;
  padding-bottom: 3px;
}
@media screen and (max-width: 767px) {
  .checkbox-parts {
    font-size: 3.8vw;
  }
}
.checkbox-parts:hover {
  color: #427fae;
}
.checkbox-parts::before {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 22px;
  height: 22px;
  border: 1px solid #ccc;
  border-radius: 2px;
  background-color: #fff;
}

input.err + .checkbox-parts::before {
  background-color: #fffafa;
  border: solid 1px #f00;
}

.checkbox-input:checked + .checkbox-parts::before {
  border-color: #427fae !important;
  background-color: #427fae;
}
.checkbox-input:checked + .checkbox-parts::after {
  content: "";
  display: block;
  position: absolute;
  top: calc(50% + 1px);
  left: 4px;
  width: 6px;
  height: 12px;
  margin-top: -5px;
  -webkit-transform: rotate(40deg) translateY(-50%);
          transform: rotate(40deg) translateY(-50%);
  border-bottom: 2px solid #fff;
  border-right: 2px solid #fff;
}

input[type=submit], input[type=reset] {
  border-radius: 0;
  -webkit-appearance: none;
  background-color: #427fae;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  width: 280px;
  max-width: 100%;
  height: 54px;
  margin: 10px 20px;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
input[type=button] {
  width: 280px;
  max-width: 100%;
  height: 56px;
  margin: 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  background-color: #427fae;
  border-radius: 4px;
  -webkit-appearance: none;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
@media screen and (max-width: 767px) {
  input[type=button] {
    height: 15vw;
  }
}
input[type=button].ipt-btn-gray {
  background-color: #EAEAEA;
  border: 1px solid #D1CFCF;
  color: #000;
  font-weight: 400;
  width: auto;
  max-width: 100%;
  height: auto;
  min-height: 40px;
  margin: 0;
  padding: 5px 25px;
}
input[type=submit].btn-back, input[type=reset].btn-back, input[type=button].btn-back {
  background-color: #D1CFCF;
}
input[type=submit]:hover, input[type=reset]:hover, input[type=button]:hover {
  opacity: 0.8;
}
input[type=submit][disabled], input[type=reset][disabled], input[type=button][disabled] {
  background-color: #7e97ab;
  color: #a4b9c9;
  border-radius: 40px;
}
@media screen and (max-width: 767px) {
  input[type=submit][disabled], input[type=reset][disabled], input[type=button][disabled] {
    border-radius: 20vw;
  }
}
input[type=submit][disabled] {
  opacity: 1;
  cursor: auto;
}

:hover input[type=reset][disabled]:hover, input[type=button][disabled]:hover {
  opacity: 1;
  cursor: auto;
}

button, [type=button], [type=reset], [type=submit], [role=button] {
  cursor: pointer;
}

input[type=file] {
  display: none;
}

.ipt-file label {
  background-color: #EAEAEA;
  border: 1px solid #D1CFCF;
  font-size: 1rem;
  min-width: 150px;
  min-height: 40px;
  text-align: center;
  padding: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

p.err-txt {
  padding: 0 1rem;
  font-size: 12px;
  color: #ff0000;
}
@media screen and (max-width: 767px) {
  p.err-txt {
    padding: 0 4vw;
  }
}

.ime-dis {
  ime-mode: disabled;
}

::-moz-selection {
  background: #333F48;
  color: #fff;
}

::selection, ::-moz-selection {
  background: #333F48;
  color: #fff;
}

::-webkit-input-placeholder, :-moz-placeholder, ::-moz-placeholder {
  color: #d4d2d2;
  font-weight: 700;
}

:-ms-input-placeholder {
  color: #d4d2d2 !important;
  font-weight: 700;
}

.clear {
  clear: both;
}

.clearfix:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  visibility: hidden;
  font-size: 0;
}

/* 背景色
 * *========================================== */
.bgCoCor {
  background-color: #5D5958;
}

/* アクセントカラー */
.acCor {
  background-color: #427fae;
}

/* FONT COLOR
 * *========================================== */
.txtGray6 {
  color: #666;
}

.txtGold {
  color: #CC993E;
}

.txtBlue {
  color: #09C;
}

.txtRed {
  color: #F00;
}

/* コーポレートカラー
 * *========================================== */
.txtCoCor {
  color: #427fae;
}

/* FONT SIZE
 * *========================================== */
.fontB {
  font-weight: 700;
}

.fontR {
  font-weight: 400;
}

.font10 {
  font-size: 10px;
}

.font11 {
  font-size: 11px;
}

.font12 {
  font-size: 12px;
}

.font13 {
  font-size: 13px;
}

.font14 {
  font-size: 14px;
}

.font15 {
  font-size: 15px;
}

.font16 {
  font-size: 16px;
}

.font18 {
  font-size: 18px;
}

.font20 {
  font-size: 20px;
}

.font26 {
  font-size: 26px;
}

/* LINE HEIGHT
 * *========================================== */
.lh10 {
  line-height: 1;
}

.lh12 {
  line-height: 1.2;
}

.lh14 {
  line-height: 1.4;
}

.lh16 {
  line-height: 1.6;
}

.lh18 {
  line-height: 1.8;
}

.lh20 {
  line-height: 2;
}

.lh24 {
  line-height: 2.4;
}

/* KOME
 * *========================================== */
.kome {
  padding-left: 1em;
  text-indent: -1em;
}

.komeH {
  padding-left: 0.5em;
  text-indent: -0.5em;
  display: block;
}

.ulKome li {
  padding-left: 1em;
  text-indent: -1em;
}

.ulKomeH li {
  padding-left: 0.5em;
  text-indent: -0.5em;
  display: block;
}

/* TEXT ALIGN
 * *========================================== */
.taC {
  text-align: center;
  margin: 0 auto;
}

.taR {
  text-align: right;
}

.taL {
  text-align: left;
}

/* LETTER SPACING
 * *========================================== */
.lsN {
  letter-spacing: normal !important;
}

.ls01 {
  letter-spacing: 0.1rem !important;
}

.ls02 {
  letter-spacing: 0.2rem !important;
}

.ls-01 {
  letter-spacing: -0.1rem !important;
}

.ls-02 {
  letter-spacing: -0.2rem !important;
}

/* WIDE
 * *========================================== */
.w100 {
  width: 100px;
}

.w120 {
  width: 120px;
}

.w150 {
  width: 150px;
}

.w240 {
  width: 240px;
}

.w400 {
  width: 400px;
}

.w500 {
  width: 500px;
}

.w700 {
  width: 700px;
}

.w20per {
  width: 20%;
}

.w40per {
  width: 40%;
}

.w55per {
  width: 55%;
}

.w60per {
  width: 60%;
}

.w80per {
  width: 80%;
}

.w85per {
  width: 85%;
}

.w100per {
  width: 100%;
}

/* HEIGHT
 * *========================================== */
.h23 {
  height: 23px;
}

.h42 {
  height: 42px;
}

.h63 {
  height: 63px;
}

.h84 {
  height: 84px;
}

.h105 {
  height: 105px;
}

.h210 {
  height: 210px;
}

/* UNDERLINE
 * *========================================== */
.undL {
  text-decoration: underline;
}

.undN {
  text-decoration: none;
}

.uchikeshi {
  text-decoration: line-through;
}

/* レスポンシブ対応
 * *========================================== */
.only_pc, .only_sp {
  font-weight: inherit;
}

@media screen and (max-width: 767px) {
  .only_pc {
    display: none !important;
  }
}

@media only screen and (min-width: 768px) {
  .only_sp {
    display: none !important;
  }
}
/*# sourceMappingURL=set.css.map */