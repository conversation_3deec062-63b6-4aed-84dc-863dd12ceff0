@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *マイページ
 * *********************************************************************** */
/* Nav
 * *========================================== */
#main #mypage-head {
  padding: 0;
}
#main #mypage-head .container {
  width: 100%;
  padding: 0;
}
#main #mypage-head .container .nav-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 0;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
  border-bottom: 1px solid #e9eaeb;
}
#main #mypage-head .container .nav-wrap .nav-content {
  width: 220px;
  max-width: 100%;
  height: 80px;
  margin: 0;
  padding: 0;
  border-radius: 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content {
    width: 25%;
    height: 18vw;
    min-height: 12vw;
    margin: 0;
  }
}
#main #mypage-head .container .nav-wrap .nav-content:hover {
  opacity: 1;
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.bidding {
  background-image: url(../img/common/icn_mypage_nav_bid.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.account {
  background-image: url(../img/common/icn_mypage_nav_account.svg);
}
#main #mypage-head .container .nav-wrap .nav-content:hover span.upload {
  margin-left: 3px;
  background-image: url(../img/common/icn_mypage_nav_upload.svg);
  background-size: 13px auto;
  background-position: center 2px;
}
#main #mypage-head .container .nav-wrap .nav-content:hover .label {
  color: #333;
}
#main #mypage-head .container .nav-wrap .nav-content a {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  height: 100%;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    padding: 4vw 0 3vw;
  }
}
#main #mypage-head .container .nav-wrap .nav-content a span {
  display: inline-block;
  content: "";
  background-position: center;
  background-size: 20px auto;
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a span {
    top: 0;
    left: calc(50% - 2.75vw);
    width: 5.5vw;
    height: 5.5vw;
    background-size: 5vw auto;
  }
}
#main #mypage-head .container .nav-wrap .nav-content a span.favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.bidding {
  background-image: url(../img/common/icn_mypage_nav_bid_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.account {
  background-image: url(../img/common/icn_mypage_nav_account_pgr.svg);
}
#main #mypage-head .container .nav-wrap .nav-content a span.upload {
  margin-left: 3px;
  background-image: url(../img/common/icn_mypage_nav_upload_pgr.svg);
  background-size: 13px auto;
  background-position: center 2px;
}
#main #mypage-head .container .nav-wrap .nav-content a .label {
  display: inline-block;
  padding: 0 0 0 0.5rem;
  color: #c9c9c9;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .container .nav-wrap .nav-content a .label {
    padding: 2vw 0 0;
    font-size: 2.2vw;
  }
}
#main #mypage-head .container .nav-wrap .nav-content.active {
  background-color: transparent;
  border-bottom: 3px solid #333;
}
#main #mypage-head .container .nav-wrap .nav-content.active a {
  cursor: default;
  pointer-events: none;
}
#main #mypage-head .container .nav-wrap .nav-content.active a:hover {
  opacity: 1;
}
#main #mypage-head .container .nav-wrap .nav-content.active a .favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .bidding {
  background-image: url(../img/common/icn_mypage_nav_bid.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .account {
  background-image: url(../img/common/icn_mypage_nav_account.svg);
}
#main #mypage-head .container .nav-wrap .nav-content.active a .upload {
  margin-left: 3px;
  background-image: url(../img/common/icn_mypage_nav_upload.svg);
  background-size: 13px auto;
  background-position: center 2px;
}
#main #mypage-head .container .nav-wrap .nav-content.active a .label {
  color: #333;
}
#main #mypage-form {
  margin: 0 0 60px;
  padding: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-form {
    margin: 0 0 40px;
    padding: 0 0 1rem;
  }
}
#main #mypage-form .container .assessment {
  width: 980px;
  max-width: 100%;
  margin: 0 auto 1rem;
  padding: 2rem;
  background-color: #ecf2f7;
  border: 1px solid #ccc;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment {
    width: 100%;
    padding: 7vw 4vw;
  }
}
#main #mypage-form .container .assessment .intro {
  margin: 0 0 2rem;
}
#main #mypage-form .container .assessment .intro p {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .intro p {
    font-size: 3.5vw;
    text-align: left;
  }
}
#main #mypage-form .container .assessment .ttl-assessment-flow {
  text-align: center;
  margin: 1rem 0;
  font-size: 1.1rem;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .ttl-assessment-flow {
    font-size: 4vw;
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 1rem 0;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow {
  width: calc((100% - 60px) / 3);
  padding: 1.5rem 1.2rem;
  background-color: #fff;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow .box-flow {
    width: 100%;
    padding: 5vw 6vw;
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  margin: 0 0 0.5rem;
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .num {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 40px;
  height: 40px;
  font-size: 1rem;
  font-family: YakuHanJP, "Hiragino Sans", Meiryo, "Helvetica Neue", "Helvetica", Arial, sans-serif;
  background-color: #427fae;
  border-radius: 2px;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .num {
    font-size: 5vw;
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .num span {
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .num span {
    font-size: 3vw;
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .text-detail {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  padding: 0 0 0 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .text-detail p {
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1.3;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .text-detail p {
    font-size: 3.5vw;
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .text-detail .btn.assessment-info {
  width: 18px;
  min-width: 18px;
  height: 18px;
  margin: 0 0 0 5px;
  padding: 0;
  background-color: transparent;
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow .pri-item .text-detail .btn.assessment-info img {
  width: 100%;
  height: auto;
}
#main #mypage-form .container .assessment .cont-assessment-flow .box-flow .ann {
  color: #ff0000;
  font-size: 0.7rem;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow .box-flow .ann {
    font-size: 3vw;
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow .arrow-assessment-flow {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 30px;
  height: auto;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow .arrow-assessment-flow {
    width: 100%;
    height: 24px;
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow .arrow-assessment-flow span {
  display: inline-block;
  width: 14px;
  height: 20px;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow .arrow-assessment-flow span {
    width: 20px;
    height: 14px;
    -webkit-transform: translateY(-25%);
            transform: translateY(-25%);
  }
}
#main #mypage-form .container .assessment .cont-assessment-flow .arrow-assessment-flow span::after {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 14px solid #427fae;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .cont-assessment-flow .arrow-assessment-flow span::after {
    border-top: 14px solid #427fae;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    border-bottom: none;
  }
}
#main #mypage-form .container .assessment .btn-wrap {
  width: 100%;
  margin: 2rem 0 1rem;
  text-align: center;
}
#main #mypage-form .container .assessment .btn-wrap button.dl-doc {
  width: auto;
  height: 60px;
  margin: 0 auto;
  padding: 0 3rem;
  background-color: #333;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .btn-wrap button.dl-doc {
    width: 100%;
    height: 14vw;
  }
}
#main #mypage-form .container .assessment .btn-wrap button.dl-doc span {
  position: relative;
  display: inline-block;
  padding: 0 26px 0 0;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .btn-wrap button.dl-doc span {
    padding: 0 6vw 0 0;
    font-size: 3.8vw;
  }
}
#main #mypage-form .container .assessment .btn-wrap button.dl-doc span::after {
  position: absolute;
  top: 3px;
  right: 0;
  content: "";
  display: inline-block;
  background: url(../img/common/icn_download_w.svg) center no-repeat;
  background-size: 17px auto;
  width: 18px;
  height: 18px;
}
@media screen and (max-width: 767px) {
  #main #mypage-form .container .assessment .btn-wrap button.dl-doc span::after {
    top: 1vw;
    background-size: 4vw auto;
    width: 4vw;
    height: 4vw;
  }
}
/*# sourceMappingURL=mypage.css.map */