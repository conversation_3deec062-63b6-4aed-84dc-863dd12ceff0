@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *マイページ
 * *********************************************************************** */
/* Nav
 * *========================================== */
#main #mypage-head {
  padding: 0;
}
#main #mypage-head .nav-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  gap: 0;
  width: 100%;
  margin: 3rem 0;
  border-bottom: 3px solid #000;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
#main #mypage-head .nav-wrap .nav-content {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  height: 60px;
  margin: 0;
  background-color: #f1f1f1;
}
@media screen and (max-width: 767px) {
  #main #mypage-head .nav-wrap .nav-content {
    width: calc((100% - 0.5rem) / 2);
    height: 50px;
    margin: 0;
  }
}
#main #mypage-head .nav-wrap .nav-content a {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  width: 100%;
  height: 100%;
  padding: 0;
}
#main #mypage-head .nav-wrap .nav-content a:hover {
  background-color: #f5f5f5;
}
#main #mypage-head .nav-wrap .nav-content a span {
  position: absolute;
  top: calc(50% - 9px);
  left: 28%;
  display: inline-block;
  content: "";
  background-position: center;
  background-size: 20px auto;
  width: 20px;
  height: 20px;
}
#main #mypage-head .nav-wrap .nav-content a span.favorite {
  background-image: url(../img/common/icn_mypage_nav_favorite.svg);
}
#main #mypage-head .nav-wrap .nav-content a span.bidding {
  background-image: url(../img/common/icn_mypage_nav_bid_w.svg);
}
#main #mypage-head .nav-wrap .nav-content a span.winning-history {
  background-image: url(../img/common/icn_mypage_nav_winning-history.svg);
}
#main #mypage-head .nav-wrap .nav-content a span.account {
  background-image: url(../img/common/icn_mypage_nav_account.svg);
}
#main #mypage-head .nav-wrap .nav-content a .label {
  display: inline-block;
  padding: 0 0 0 1.5rem;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.2;
}
#main #mypage-head .nav-wrap .nav-content.active {
  background-color: #333;
}
#main #mypage-head .nav-wrap .nav-content.active a {
  cursor: default;
}
#main #mypage-head .nav-wrap .nav-content.active a:hover {
  opacity: 1;
}
#main #mypage-head .nav-wrap .nav-content.active .label {
  color: #fff;
}
#main #mypage-form {
  margin: 0 0 60px;
  padding: 0 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #mypage-form {
    margin: 0 0 40px;
    padding: 0 0 1rem;
  }
}
/*# sourceMappingURL=mypage.css.map */