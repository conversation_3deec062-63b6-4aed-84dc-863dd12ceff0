@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *会員登録
 * *********************************************************************** */
#main #entry-info {
  margin: 60px 0 60px;
  padding: 0 1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-info {
    margin-top: 40px;
  }
}
#main #entry-info dl {
  border: 1px solid #bebebe;
  background-color: #efefef;
  padding: 50px 50px 40px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl {
    padding: 15px 15px 20px;
  }
}
#main #entry-info dl dt {
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  color: #427fae;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dt {
    font-size: 17px;
    text-align: left;
  }
}
#main #entry-info dl dd {
  margin-top: 30px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd {
    margin-top: 20px;
  }
}
#main #entry-info dl dd p.jp-document {
  font-weight: 700;
  text-align: center;
  font-size: 18px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd p.jp-document {
    font-size: 1rem;
  }
}
#main #entry-info dl dd ol {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol {
    display: block;
  }
}
#main #entry-info dl dd ol li {
  width: 300px;
  background-color: #fff;
  margin-top: 20px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li {
    width: 100% !important;
    padding-bottom: 30px;
  }
}
#main #entry-info dl dd ol li:nth-of-type(2) {
  width: 380px;
}
#main #entry-info dl dd ol li a {
  display: block;
  text-align: center;
  width: 100%;
  height: 100%;
  padding: 0 15px 20px;
}
#main #entry-info dl dd ol li p.entry-info-item {
  position: relative;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  padding-top: 25px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li p.entry-info-item {
    letter-spacing: 0;
    font-size: 1rem;
  }
}
#main #entry-info dl dd ol li p.entry-info-item::after {
  content: "?";
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  color: #427fae;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #427fae;
  line-height: 18px;
  text-align: center;
}
#main #entry-info dl dd ol li p.entry-info-item span {
  display: inline-block;
  padding-top: 5px;
  padding-left: 18px;
  font-size: 24px;
  color: #fff;
  font-weight: 700;
  position: absolute;
  top: 0;
  left: -15px;
  z-index: 0;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li p.entry-info-item span {
    font-size: 20px;
    padding-top: 6px;
    padding-left: 16px;
  }
}
#main #entry-info dl dd ol li p.entry-info-item span::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  border-top: 40px solid #427fae;
  border-right: 40px solid transparent;
  border-bottom: 40px solid transparent;
  border-left: 40px solid #427fae;
  z-index: -1;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li p.entry-info-item span::before {
    border-width: 35px;
  }
}
#main #entry-info dl dd ol li img {
  display: inline-block;
  margin-top: 5px;
  -webkit-transition: all 0.08s linear;
  transition: all 0.08s linear;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li img {
    height: 130px;
  }
}
#main #entry-info dl dd ol li a:hover img {
  opacity: 0.8;
}
#main #entry-info dl dd ol li p.entry-info-note {
  color: #f00;
  text-align: center;
  margin-top: 5px;
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd ol li p.entry-info-note {
    margin-bottom: -20px;
    font-size: 14px;
  }
}
#main #entry-info dl dd p.kojin {
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd p.kojin {
    text-align: left;
    text-indent: -1em;
    padding-left: 1em;
  }
}
#main #entry-info dl dd .entry-info-btn {
  margin: 40px auto 0;
  width: 360px;
}
@media screen and (max-width: 767px) {
  #main #entry-info dl dd .entry-info-btn {
    width: 100%;
    margin-top: 20px;
  }
}
#main #entry-info p.entry-info-att {
  margin-top: 30px;
  font-size: 16px;
}
#main #entry-info p.entry-info-att a {
  text-decoration: underline;
  color: #427fae;
}
#main #entry-form {
  width: 980px;
  max-width: calc(100% - 2rem);
  margin: 60px auto 2rem;
  padding: 60px 3rem 60px;
  background-color: #f7f7f7;
}
@media screen and (max-width: 1080px) {
  #main #entry-form {
    padding: 60px 1.5rem 60px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form {
    margin-top: 40px;
    margin-bottom: 40px;
    padding: 15px 15px 20px;
  }
}
#main #entry-form p.entry-form-info {
  text-align: center;
  font-weight: 700;
  font-size: 18px;
}
@media screen and (max-width: 767px) {
  #main #entry-form p.entry-form-info {
    text-align: left;
    font-size: 17px;
  }
}
#main #entry-form em.req {
  display: inline-block;
  background-color: #E80000;
  width: 35px;
  height: 20px;
  color: #fff;
  font-weight: 700;
  font-size: 12px;
  text-align: center;
  line-height: 19px;
}
#main #entry-form p.entry-form-info em.req {
  margin-right: 5px;
  position: relative;
  top: -2px;
}
#main #entry-form table.tbl-entry {
  width: 100%;
  margin: 0 auto;
}
#main #entry-form p.entry-form-info + table.tbl-entry {
  margin-top: 40px;
}
#main #entry-form table.tbl-entry {
  width: 100%;
}
#main #entry-form table.tbl-entry tr {
  padding: 0.3rem 0;
}
#main #entry-form table.tbl-entry tr th {
  font-size: 1rem;
  font-weight: 700;
  vertical-align: middle;
  width: 280px;
  position: relative;
  padding: 0 2rem;
  line-height: 1.2;
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry tr th {
    width: 230px;
    min-width: 190px;
    padding: 0 1rem;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr th {
    display: block;
    width: 100%;
    padding: 0;
    font-size: 1rem;
  }
}
#main #entry-form table.tbl-entry tr th em.req {
  position: absolute;
  top: 50%;
  right: 10px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
#main #entry-form table.tbl-entry tr td {
  width: calc(100% - 1rem);
  padding: 15px 0;
  position: relative;
  line-height: 1.2;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td {
    display: block;
    width: 100%;
    padding: 10px 0 20px;
  }
}
#main #entry-form table.tbl-entry tr td p.privacy-link {
  margin-top: -20px;
  font-size: 1rem;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry tr td p.privacy-link {
    margin-top: -40px;
  }
}
#main #entry-form table.tbl-entry tr td p.privacy-link a {
  text-decoration: underline;
  color: #427fae;
}
#main #entry-form table.tbl-entry tr td .checkbox-parts {
  font-size: 1.1rem;
}
#main.comp #entry-form table.tbl-entry td, #main[class^=contact-] #entry-form table.tbl-entry td {
  font-size: 1.1rem;
}
#main.comp #entry-form table.tbl-entry tr.att-use {
  display: none;
}
#main #entry-form table.tbl-entry td .ipt-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td .ipt-wrap {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}
#main #entry-form table.tbl-entry td .ipt-wrap input.zip-search {
  margin: 5px 0 5px 15px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td .ipt-wrap input.zip-search {
    padding-left: 5px;
    padding-right: 5px;
    margin: 0 0 0 15px;
  }
}
#main #entry-form table.tbl-entry td .ipt-wrap span.int-no {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background-color: #F7F7F7;
  border: 1px solid #E3E3E3;
  border-right: none;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
  width: 55px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td .ipt-wrap span.int-no {
    width: calc(100% - 55px) !important;
  }
}
#main #entry-form table.tbl-entry td .ipt-wrap span.ipt-rule {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 10px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td .ipt-wrap span.ipt-rule {
    width: calc(100% - 55px) !important;
    margin-left: 0;
    margin-top: 3px;
  }
}
#main #entry-form table.tbl-entry td .select-style {
  width: 460px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td .select-style {
    width: 100%;
    max-width: 100%;
  }
}
#main #entry-form table.tbl-entry td input.iptW-M {
  width: 460px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry td input.iptW-M {
    width: 360px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td input.iptW-M {
    width: 100%;
    max-width: 100%;
  }
}
#main #entry-form table.tbl-entry td input.iptW-MM {
  width: 300px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 1080px) {
  #main #entry-form table.tbl-entry td input.iptW-MM {
    width: 305px;
  }
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td input.iptW-MM {
    width: calc(100% - 55px);
    width: calc(100% - 55px);
  }
}
#main #entry-form table.tbl-entry td input.iptW-S {
  width: 200px;
  max-width: calc(100% - 40px);
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td input.iptW-S {
    width: calc(100% - 40px - 19px);
  }
}
#main #entry-form table.tbl-entry td textarea {
  width: 100%;
  height: 200px;
  resize: vertical;
}
#main #entry-form table.tbl-entry td .ipt-file-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td .ipt-file-box {
    display: block;
  }
}
#main #entry-form table.tbl-entry td .ipt-file-box label {
  margin-right: 10px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td .ipt-file-box label {
    margin-right: 0;
    margin-bottom: 5px;
  }
}
#main #entry-form table.tbl-entry td .file-up-att {
  margin-top: -20px;
  font-size: 16px;
}
@media screen and (max-width: 767px) {
  #main #entry-form table.tbl-entry td .file-up-att {
    margin-top: -40px;
  }
}
#main #entry-form .btn-form {
  margin-top: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
#main.comp p.comp-msg {
  margin: 0 0 60px;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
}
@media screen and (max-width: 767px) {
  #main.comp p.comp-msg {
    margin: 0 0 40px;
    text-align: left;
    font-size: 17px;
  }
}
#main.contact-used #target-item {
  margin-top: 60px;
  margin-bottom: -30px;
}
#main.contact-used #target-item ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  border: 1px solid #EAEAEA;
}
#main.contact-used #target-item ul li + li {
  border-top: none;
}
#main.contact-used #target-item ul li .target-item-img {
  width: 267px;
  text-align: center;
}
#main.contact-used #target-item ul li .target-item-txt {
  width: calc(100% - 267px);
  padding: 30px 0 15px 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  letter-spacing: 0;
}
#main.contact-used #target-item ul li .target-item-txt p.target-item-no {
  background-color: #000;
  color: #fff;
  font-weight: 700;
  line-height: 1;
  display: inline-block;
  padding: 6px 10px 8px;
  margin-right: 10px;
}
#main.contact-used #target-item ul li .target-item-txt p.target-item-system {
  color: #01a7ac;
  font-size: 14px;
  font-weight: 700;
  background-color: #E8FEFF;
  border: 1px solid #01A7AC;
  border-radius: 100vh;
  padding: 2px 15px 0;
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-top: 10px;
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data dl {
  min-width: 33.3333333333%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-right: 15px;
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data dl dt, #main.contact-used #target-item ul li .target-item-txt .target-item-data dl dd {
  font-size: 16px;
  font-weight: 700;
  padding: 4px 0;
}
#main.contact-used #target-item ul li .target-item-txt .target-item-data dl dt {
  color: #01A7AC;
  min-width: 6.5em;
  padding-right: 10px;
  -ms-flex-negative: 0;
      flex-shrink: 0;
}
/*# sourceMappingURL=entry.css.map */