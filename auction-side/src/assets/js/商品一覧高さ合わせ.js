/////[商品一覧]商品名高さ合わせ
$(document).ready(function () {
  equalizeItemNameHeight();

  // ウィンドウサイズ変更時も再判定
  $(window).on("resize", function () {
    equalizeItemNameHeight();
  });
});

function equalizeItemNameHeight() {
  var itemNames = $("li p.item-name");

  // 一度リセット
  itemNames.css("height", "auto");

  // スマホ画面（767px以下）では高さを揃えない
  if (window.innerWidth <= 767) {
    return;
  }

  // 高さの最大値を取得
  var maxHeight = 0;
  itemNames.each(function () {
    var height = $(this).height();
    if (height > maxHeight) {
      maxHeight = height;
    }
  });

  // 全ての item-name に最大値を適用
  itemNames.height(maxHeight);
}