import { createI18n } from 'vue-i18n'
import { en } from './en'
import { ja } from './ja'

const messages = {
  en,
  ja
}

const i18n = createI18n({
  legacy: false, // Vuetify does not support the legacy mode of vue-i18n
  warnHtmlMessage: false, // Disable warning on HTML messages
  locale: 'ja',
  fallbackLocale: 'en', // choose which language to use when your preferred language lacks a translation
  messages
})

export default i18n
