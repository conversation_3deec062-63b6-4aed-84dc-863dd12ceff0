import { reactive } from 'vue'

export const eventBus = reactive({
  events: {},

  on: (event, callback) => {
    if (!eventBus.events[event]) {
      eventBus.events[event] = []
    }
    eventBus.events[event].push(callback)
  },

  off: (event, callback) => {
    if (!eventBus.events[event]) return
    eventBus.events[event] = eventBus.events[event].filter((cb) => cb !== callback)
  },

  emit: (event, ...args) => {
    if (!eventBus.events[event]) return
    eventBus.events[event].forEach((callback) => callback(...args))
  }
})
