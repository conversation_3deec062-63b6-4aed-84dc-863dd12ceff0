import axios, { type AxiosError, type AxiosResponse } from 'axios'
import pako from 'pako'
import { useRouter } from 'vue-router'
import { useCookies } from 'vue3-cookies'
import { LOCAL_STORE_LABEL, PATH_NAME } from '../defined/const'
import { useLanguageStore } from '../stores/language'
import { useReloadAttemptStore } from '../stores/reload-attempt'
import { useLoaderStore } from '../stores/ui'

// API related type definitions
export type ApiRequestParams = {
  [key: string]: any
  languageCode?: string
}

export type ApiHeaders = {
  [key: string]: string | undefined
  authorization?: string
  'Content-Type'?: string
}

export type UseApiReturn = {
  apiExecute: <T = any>(
    path: string,
    params?: ApiRequestParams,
    fileUpload?: boolean,
    headers?: ApiHeaders
  ) => Promise<T>
  parseHtmlResponseError: (error: AxiosError | any) => any
}

export type BufferResponse = {
  type: 'Buffer'
  data: number[]
}

const VITE_API_ENDPOINT = import.meta.env.VITE_API_ENDPOINT

// Mock data for development
const getMockData = (path: string): any => {
  switch (path) {
    case 'public/get-item-search-constants':
      return [
        { key_string: 'PRODUCT_CATEGORY', value1: '1', value2: 'カテゴリ1' },
        { key_string: 'PRODUCT_CATEGORY', value1: '2', value2: 'カテゴリ2' },
        { key_string: 'TAX_RATE', value1: '10', value2: '10%' },
        { key_string: 'PITCH_FOLLOW_BID_PRICE', value1: '100', value2: '100円' }
      ]
    case 'public/search-auction-items':
      return {
        items: [
          {
            exhibition_item_no: 'MOCK001',
            item_no: 'ITEM001',
            category_id: 1,
            free_field: {
              productName: 'モックアイテム1'
            },
            bid_status: {
              current_price: 1000,
              bid_price: 1100,
              bid_quantity: 1,
              tax_rate: 10
            },
            attention_info: {
              bid_count: 5
            },
            start_datetime: new Date().toISOString(),
            end_datetime: new Date(Date.now() + 86400000).toISOString(),
            exhibition_no: 'EXH001'
          }
        ],
        total_count: 1,
        current_count: 1,
        status: 'success'
      }
    case 'public/get-new-notices':
      return {
        notices: [
          {
            notice_no: 1,
            title: 'モック通知',
            sub_title: 'テスト用通知',
            body: 'これはモック環境での通知です。',
            create_date: new Date().toISOString(),
            display_code: 1,
            file: [],
            link_url: '',
            title1: 'モック通知'
          }
        ],
        total_count: 1,
        status: 'success'
      }
    case 'private/get-change-info-constants':
      return {
        constants: [
          { key: 'country', value1: 'JP', value2: '日本' },
          { key: 'country', value1: 'US', value2: 'アメリカ' }
        ],
        member: {
          nickname: 'モックユーザー',
          email: '<EMAIL>',
          country: 'JP',
          companyName: 'モック会社',
          tel: '03-1234-5678'
        },
        status: 'success'
      }
    case 'private/change-member-info':
      return {
        status: 'success',
        message: 'Member information updated successfully'
      }
    case 'private/change-member-password':
      return {
        status: 'success',
        message: 'Password changed successfully'
      }
    case 'private/withdraw-member':
      return {
        status: 'success',
        message: 'Member withdrawal completed'
      }
    case '/get-auction-common-constants':
    case 'get-auction-common-constants':
      return [
        { key: 'CURRENCY', value1: 'JPY', value2: '円' },
        { key: 'TIMEZONE', value1: 'Asia/Tokyo', value2: 'JST' },
        { key: 'LANGUAGE', value1: 'ja', value2: '日本語' }
      ]
    case 'get-member-regist-constants':
      return [
        { key_string: 'COUNTRY_CODE', value1: 'JP', value2: '日本' },
        { key_string: 'COUNTRY_CODE', value1: 'US', value2: 'アメリカ' },
        { key_string: 'COUNTRY_CODE', value1: 'CN', value2: '中国' }
      ]
    case 'request-member':
      return {
        status: 'success',
        message: 'Member registration completed successfully'
      }
    case 'private/get-successful-bid-history':
      return {
        items: [
          {
            exhibition_item_no: 'MOCK_BID_001',
            item_no: 'ITEM_BID_001',
            category_id: 1,
            free_field: {
              productName: 'モック落札商品1'
            },
            bid_status: {
              current_price: 5000,
              bid_price: 5500,
              bid_quantity: 1,
              tax_rate: 10
            },
            attention_info: {
              bid_count: 8
            },
            start_datetime: new Date(Date.now() - 86400000 * 7).toISOString(),
            end_datetime: new Date(Date.now() - 86400000 * 5).toISOString(),
            exhibition_no: 'EXH_BID_001'
          }
        ],
        count: 1,
        exhibition_group: [],
        isMoreLimit: false
      }
    default:
      return {
        status: 'success',
        message: 'Mock response for development'
      }
  }
}

// Check if we're in mock mode (development with mock token)
const isMockMode = (): boolean => {
  const { cookies } = useCookies()
  const token = cookies.get(LOCAL_STORE_LABEL.SESSION_TOKEN)
  const isDev = import.meta.env.DEV
  const isMockToken = token === 'mock-token-for-development'

  // In development, if we have the mock token, use mock mode
  // Also use mock mode if we're in dev and the API endpoint contains cloudfront (production API)
  const isProductionAPI = VITE_API_ENDPOINT.includes('cloudfront.net')
  const mockMode = isDev && (isMockToken || isProductionAPI)

  // console.log('🔍 Mock mode check:', { isDev, token, isMockToken, isProductionAPI, mockMode })
  return mockMode
}

/**
 * @module composables/useApi
 * API composable with TypeScript support
 */
export default function useApi(): UseApiReturn {
  const router = useRouter()
  const loader = useLoaderStore()
  const { cookies } = useCookies()
  const attempt = useReloadAttemptStore()
  const languageStore = useLanguageStore()

  /**
   * Execute API request with proper typing
   * @param path - API endpoint path
   * @param params - Request parameters
   * @param fileUpload - Whether this is a file upload request
   * @param headers - Additional headers
   * @returns Promise with typed response
   */
  const apiExecute = <T = any>(
    path: string,
    params: ApiRequestParams = {},
    fileUpload: boolean = false,
    headers: ApiHeaders = {}
  ): Promise<T> => {
    loader.setLoading(true)

    // Check if we're in mock mode and return mock data
    if (isMockMode()) {
      console.log(`🎭 Mock API ${path}:`, params)
      const mockData = getMockData(path)
      loader.setLoading(false)
      return Promise.resolve(mockData as T)
    } else {
      console.log(`🌐 Real API ${path}:`, params)
    }

    return Promise.resolve()
      .then(() => {
        const axs = axios.create({
          baseURL: VITE_API_ENDPOINT,
          timeout: 30000,
          headers: {
            'Content-Type': fileUpload ? 'application/octet-stream' : 'application/json'
          }
        })

        const config = {
          headers: {
            authorization: cookies.get(LOCAL_STORE_LABEL.SESSION_TOKEN) || 'public',
            ...headers
          }
        }

        return axs.post(
          path,
          Object.assign({ languageCode: languageStore.language || 'ja' }, params),
          config
        )
      })
      .then((response: AxiosResponse) => {
        const data = isBufferResponse(response.data)
          ? pako.inflate(response.data.data, { to: 'string' })
          : response.data

        if (response.headers['content-type']?.includes('application/json')) {
          try {
            const parsedData = JSON.parse(data.toString())
            console.log(`⚙️API ${path}: `, parsedData)
            return parsedData as T
          } catch (e) {
            console.error('Error parsing JSON:', data.toString(), e)
            throw new Error('Invalid JSON response')
          }
        } else if (response.headers['content-type']?.includes('text/html')) {
          console.warn(
            '⚠️ Possibly error cause have not deploy api gateway yet or overridden token cause multiple apps on localhost'
          )
          console.error('HTML response received ...', data.toString())
          return { error: 'HTML response received', rawResponse: data.toString() } as T
        } else {
          throw new Error('Unexpected response type')
        }
      })
      .catch((error: AxiosError) => {
        console.error('API Request Failed:', error.response?.data || error.message || error)
        loader.setLoading(false)
        return Promise.reject(error)
      })
      .finally(() => {
        loader.setLoading(false)
      })
  }

  /**
   * Parse HTML response errors with proper typing
   * @param error - Axios error or any error object
   * @returns Parsed error data
   */
  const parseHtmlResponseError = (error: AxiosError | any): any => {
    console.log('error', error)

    if (error.response && (error.response.status === 400 || error.response.status === 409)) {
      // SQL errors to display on screen
      if (error.response.data.errors) {
        return error.response.data.errors
      }
      return error.response.data
    }

    if (error.response && error.response.status === 401) {
      console.log('trying to remove cookies')
      cookies.remove(LOCAL_STORE_LABEL.SESSION_TOKEN)
      cookies.remove(LOCAL_STORE_LABEL.SESSION_USERNAME)
      console.log(router.currentRoute.value.path)
      if (router.currentRoute.value.path !== PATH_NAME.LOGIN) {
        router.push('/')
      }
      return error.response.data
    }

    if (error.response && error.response.status === 403) {
      cookies.remove(LOCAL_STORE_LABEL.SESSION_TOKEN)
      cookies.remove(LOCAL_STORE_LABEL.SESSION_USERNAME)
      router.push('/')
      return error?.response?.data
    }

    if (error.code === 'ERR_NETWORK') {
      cookies.remove(LOCAL_STORE_LABEL.SESSION_TOKEN)
      cookies.remove(LOCAL_STORE_LABEL.SESSION_USERNAME)
      if (attempt.isMaxAttemptsReached()) {
        console.error('An error occurred:', error)
        attempt.reset()
      } else {
        attempt.increment()
        console.log(
          `Network error occurred. Reload attempt ${attempt.reloadAttempts} of ${attempt.maxReloadAttempts}.`
        )
        location.reload()
      }
      return error.message
    }

    return error?.response?.data || error
  }

  return {
    apiExecute,
    parseHtmlResponseError
  }
}

/**
 * Type guard to check if response data is a Buffer response
 * @param data - Response data to check
 * @returns True if data is BufferResponse
 */
function isBufferResponse(data: any): data is BufferResponse {
  return data && typeof data === 'object' && data.type === 'Buffer' && Array.isArray(data.data)
}
