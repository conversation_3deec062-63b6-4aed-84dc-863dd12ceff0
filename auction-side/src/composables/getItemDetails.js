import { useItemDetailStore } from '../stores/item-detail'
import { useSearchResultStore } from '../stores/search-results'
import useApi from './useApi'

/**
 * 商品詳細関連の処理
 */
export default function useGetItemDetails() {
  const { apiExecute, parseHtmlResponseError } = useApi()
  const itemDetailStore = useItemDetailStore()

  const getConstants = () => {
    const params = {}
    return apiExecute('public/get-item-detail-constants', params).then((response) => {
      itemDetailStore.setConstants(response)
      return Promise.resolve()
    })
  }

  const setPitchButton2 = (val) => {
    itemDetailStore.pitchButton2 = val
  }

  const setPitchButton3 = (val) => {
    itemDetailStore.pitchButton3 = val
  }

  const search = async (manageNo, path) => {
    const params = {
      manageNo,
      path
    }
    await apiExecute('public/refresh-item', params)
      .then((response) => {
        // Object.assign(searchResult, response)
        const searchResultStore = useSearchResultStore()
        searchResultStore.setProductDetails(response)

        // Set pitch button value
        if (searchResultStore.productDetails) {
          const pitchFollowPrice = searchResultStore.productDetails.bid_status.pitch_width
          setPitchButton2(pitchFollowPrice)
          setPitchButton3(pitchFollowPrice)
        }
      })
      .catch((error) => parseHtmlResponseError(error))
  }

  return { search, getConstants, setPitchButton2, setPitchButton3 }
}
